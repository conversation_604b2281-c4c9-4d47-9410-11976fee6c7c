# Quality Upgrade and Real Debrid Cleanup Implementation

## Overview

This implementation adds functionality to automatically remove old downloads from Real Debrid when a better quality version becomes available. This ensures that only one episode remains in Real Debrid, preventing storage waste and confusion.

**Key Enhancement**: The system now fully integrates with Quality Profiles, respecting user-defined upgrade rules, allowed qualities, and upgrade limits.

## Key Features

### 1. Quality Profile Integration

The system now respects Quality Profile settings:

- **Allowed Qualities**: Only qualities marked as `allowed=True` in `QualityProfileItem` are considered for upgrades
- **Upgrade Until Quality**: Stops upgrading once the `upgrade_until_quality` is reached
- **Allow Upgrades**: Respects the `allow_upgrades` setting - if disabled, no upgrades occur
- **Quality Order**: Uses the `order` field in `QualityProfileItem` to determine quality preferences

### 2. Enhanced Quality Comparison System

Added comprehensive quality comparison methods to the `MediaQuality` model:

**Fallback Scoring** (when no profile is available):
- **Resolution Priority**: 4K (4000) > 1080p (1080) > 720p (720) > 480p (480) > SD (240)
- **Source Priority**: REMUX (1000) > BluRay (900) > WEB-DL (800) > WEBRip (700) > WEB (600) > HDTV (500) > DVD (400)
- **Codec Priority**: AV1 (300) > h265/HEVC (200) > h264/AVC (100) > MPEG2 (50)

**Profile-Based Comparison** (preferred method):
- Uses `QualityDefinition` matching to find corresponding profile items
- Compares `order` values from `QualityProfileItem`
- Respects profile upgrade rules and limits

Methods added:
- `is_better_than(other_quality, quality_profile=None)`: Profile-aware quality comparison
- `is_significantly_better_than(other_quality, quality_profile=None, threshold=100)`: Prevents minor upgrades
- `_is_better_than_with_profile()`: Internal profile-based comparison logic
- `_find_matching_quality_definition()`: Maps MediaQuality to QualityDefinition

### 2. Download Status Cleanup

Enhanced `DownloadStatus` model with:

- `get_quality_object()`: Extracts quality info from torrent title
- `cleanup_real_debrid()`: Removes torrent from Real Debrid and clears IDs

### 3. Monitoring Command Enhancements

Modified `monitor_downloads.py` to:

- Detect quality upgrades when episodes become available in Plex
- Automatically clean up old Real Debrid downloads when better quality is detected
- Use significant quality difference threshold to avoid unnecessary cleanups

### 4. Download Initiation Logic

Updated both manual and auto-download flows in `views.py`:

#### Manual Downloads (`jackett_download`)
- Check for existing active downloads
- Allow quality upgrades but prevent duplicate active downloads
- Clean up old Real Debrid downloads when starting a better quality download

#### Auto Downloads (`auto_download_episode`)
- Skip episodes with active downloads
- Only proceed with quality upgrades for completed downloads
- Clean up old downloads before starting new ones
- Skip downloads that aren't quality improvements

## Workflow Examples

### Scenario 1: Manual Quality Upgrade with Profile
1. User has 720p WEB-DL episode downloaded and completed
2. Show has "HD Profile" with allowed qualities: 720p WEB-DL (order: 1), 1080p WEB-DL (order: 2), 1080p BluRay (order: 3)
3. User manually selects 1080p BluRay torrent
4. System checks profile: 1080p BluRay (order: 3) > 720p WEB-DL (order: 1) = upgrade allowed
5. Old 720p download is removed from Real Debrid
6. New 1080p BluRay download starts

### Scenario 2: Auto-Download Respects Upgrade Limit
1. Episode has completed 1080p BluRay download
2. Show profile has `upgrade_until_quality` set to "1080p BluRay"
3. Auto-download finds 4K WEB-DL torrent
4. System checks profile: upgrade limit reached, no upgrade allowed
5. 4K torrent is rejected, existing 1080p BluRay remains

### Scenario 3: Profile Disables Upgrades
1. Episode has completed 720p WEB-DL download
2. Show profile has `allow_upgrades = False`
3. User tries to manually download 1080p BluRay
4. System checks profile: upgrades disabled
5. Download is rejected with error message

### Scenario 4: Monitoring Detection with Profile Rules
1. Episode download completes and appears in Plex with better quality than expected
2. Monitor detects quality upgrade during availability check
3. System uses show's quality profile to determine if upgrade is significant
4. Only removes inferior quality downloads if profile rules allow the upgrade

## Quality Comparison Examples

### Profile-Based Comparison (Preferred)
With "Test HD Profile":
- 720p WEB-DL (order: 1) → 1080p WEB-DL (order: 2): **Upgrade allowed**
- 1080p WEB-DL (order: 2) → 1080p BluRay (order: 3): **Upgrade allowed**
- 1080p BluRay → 4K WEB-DL: **Upgrade blocked** (reached upgrade_until_quality)
- Any upgrade with `allow_upgrades = False`: **All upgrades blocked**

### Fallback Score-Based Comparison
When no profile is available:
- 720p WEB-DL h264: 1620 points
- 1080p WEB-DL h264: 1980 points
- 1080p BluRay h264: 2080 points
- 4K WEB-DL h265: 5000 points

## Configuration

### Profile-Based Thresholds
- **Order difference threshold**: Default 1 (can be customized)
- **Significant upgrade**: Requires order difference > threshold
- **Respects profile limits**: Always honors `upgrade_until_quality` and `allow_upgrades`

### Fallback Thresholds (no profile)
- **Significant upgrade threshold**: 100 points
- **Examples**: 1080p WEB-DL → 720p WEB-DL (360 points = significant), 1080p BluRay → 1080p WEB-DL (100 points = not significant)

## Error Handling

- Failed Real Debrid cleanup is logged but doesn't prevent new downloads
- Quality comparison failures default to allowing the download
- Multiple active downloads are handled gracefully
- Dry-run mode supported in monitoring command

## Benefits

1. **Storage Efficiency**: Only one version per episode in Real Debrid
2. **Quality Profile Compliance**: Fully respects user-defined quality preferences and limits
3. **Intelligent Upgrades**: Only upgrades when profile rules allow and quality is significantly better
4. **User Control**: Manual downloads can override auto-downloads (if profile allows)
5. **Safe Operation**: Profile-based thresholds prevent unnecessary churn
6. **Monitoring Integration**: Works with existing Plex/Overseerr monitoring
7. **Flexible Configuration**: Works with or without quality profiles (fallback to scoring system)

## Files Modified

- `media_requests/models.py`: Quality comparison and cleanup methods
- `media_requests/management/commands/monitor_downloads.py`: Quality upgrade detection
- `media_requests/views.py`: Download initiation logic with cleanup
- `media_requests/real_debrid_service.py`: Cleanup functionality (already existed)
