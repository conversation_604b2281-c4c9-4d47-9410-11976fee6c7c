from django.core.management.base import BaseCommand
from django.conf import settings
from django.utils import timezone
from media_requests.models import DownloadStatus, Episode
from media_requests.real_debrid_service import RealDebridService, RealDebridAPIError
from media_requests.services import OverseerrDataSyncService, OverseerrAPIError, PlexClient, PlexAPIError
import logging
import time

logger = logging.getLogger(__name__)


class Command(BaseCommand):
    help = 'Monitor download statuses and update them based on Real Debrid and Overseerr status'

    def add_arguments(self, parser):
        parser.add_argument(
            '--dry-run',
            action='store_true',
            help='Run without making any database changes',
        )
        parser.add_argument(
            '--once',
            action='store_true',
            help='Run once instead of continuously',
        )

    def handle(self, *args, **options):
        self.dry_run = options['dry_run']
        self.run_once = options['once']

        if self.dry_run:
            self.stdout.write(
                self.style.WARNING('DRY RUN MODE - No database changes will be made')
            )

        # Check if Real Debrid is configured
        if not getattr(settings, 'REAL_DEBRID_API_KEY', ''):
            self.stdout.write(
                self.style.ERROR('REAL_DEBRID_API_KEY is not configured. Exiting.')
            )
            return

        # Check if Overseerr is configured
        if not getattr(settings, 'OVERSEERR_API_KEY', '') or not getattr(settings, 'OVERSEERR_BASE_URL', ''):
            self.stdout.write(
                self.style.WARNING('Overseerr is not configured. Episode availability monitoring will be disabled.')
            )
            overseerr_enabled = False
        else:
            overseerr_enabled = True

        # Check if Plex is configured
        plex_enabled = bool(getattr(settings, 'PLEX_TOKEN', '') and getattr(settings, 'PLEX_BASE_URL', ''))
        if plex_enabled:
            self.stdout.write(
                self.style.SUCCESS('Plex is configured. Will use direct Plex API for quality checks.')
            )
        else:
            self.stdout.write(
                self.style.WARNING('Plex is not configured. Quality checks will rely on Overseerr data only.')
            )

        self.stdout.write(
            self.style.SUCCESS('Starting download monitoring...')
        )

        try:
            rd_service = RealDebridService()

            if overseerr_enabled:
                sync_service = OverseerrDataSyncService()
            else:
                sync_service = None

            # Initialize Plex client if available
            plex_client = None
            if plex_enabled:
                try:
                    plex_client = PlexClient()
                    self.stdout.write(
                        self.style.SUCCESS('Plex client initialized successfully.')
                    )
                except PlexAPIError as e:
                    self.stdout.write(
                        self.style.ERROR(f'Failed to initialize Plex client: {e}')
                    )
                    plex_client = None

            if self.run_once:
                self._monitor_cycle(rd_service, sync_service, plex_client)
            else:
                # Run continuously
                while True:
                    try:
                        self._monitor_cycle(rd_service, sync_service, plex_client)

                        # Wait 60 seconds before next check
                        self.stdout.write('Waiting 60 seconds before next check...')
                        time.sleep(60)

                    except KeyboardInterrupt:
                        self.stdout.write(
                            self.style.SUCCESS('Monitoring stopped by user.')
                        )
                        break
                    except Exception as e:
                        self.stdout.write(
                            self.style.ERROR(f'Error in monitoring cycle: {e}')
                        )
                        # Wait a bit before retrying
                        time.sleep(30)

        except RealDebridAPIError as e:
            self.stdout.write(
                self.style.ERROR(f'Real Debrid API error: {e}')
            )
        except Exception as e:
            self.stdout.write(
                self.style.ERROR(f'Unexpected error: {e}')
            )

    def _monitor_cycle(self, rd_service, sync_service, plex_client):
        """Run one monitoring cycle"""
        self.stdout.write('Starting monitoring cycle...')

        # Get all active downloads
        active_downloads = DownloadStatus.objects.filter(
            status__in=['pending', 'caching', 'downloading', 'monitoring']
        ).select_related('episode__season__tv_show')

        if not active_downloads.exists():
            self.stdout.write('No active downloads to monitor.')
            return

        self.stdout.write(f'Monitoring {active_downloads.count()} active downloads...')

        for download in active_downloads:
            try:
                self._check_download_status(download, rd_service, sync_service, plex_client)
            except Exception as e:
                logger.error(f'Error checking download {download.id}: {e}')
                self.stdout.write(
                    self.style.ERROR(f'Error checking download {download.id}: {e}')
                )

    def _check_download_status(self, download, rd_service, sync_service, plex_client):
        """Check and update status for a single download"""
        episode = download.episode
        show_title = episode.season.tv_show.title
        episode_str = f"S{episode.season.season_number:02d}E{episode.episode_number:02d}"

        self.stdout.write(f'Checking {show_title} {episode_str}...')

        # Update last checked time
        if not self.dry_run:
            download.last_checked_at = timezone.now()
            download.save(update_fields=['last_checked_at'])

        if download.status in ['pending', 'caching', 'downloading']:
            # Check Real Debrid status
            if download.real_debrid_id:
                torrent_info = rd_service.get_torrent_info(download.real_debrid_id)

                if torrent_info:
                    status = torrent_info.get('status', '')
                    progress = torrent_info.get('progress', 0)

                    self.stdout.write(f'  Real Debrid status: {status} ({progress}%)')

                    # Update cache progress for caching downloads
                    if download.status == 'caching' and not self.dry_run:
                        download.cache_progress = progress
                        download.save(update_fields=['cache_progress'])

                        # Check for cache timeout
                        from media_requests.models import AppSettings
                        settings = AppSettings.get_settings()

                        if download.cache_check_started_at:
                            cache_duration = timezone.now() - download.cache_check_started_at
                            timeout_hours = settings.real_debrid_cache_timeout_hours

                            if cache_duration.total_seconds() > (timeout_hours * 3600):
                                self.stdout.write(f'  Cache timeout reached for {show_title} {episode_str}')
                                download.status = 'failed'
                                download.error_message = f'Cache timeout after {timeout_hours} hours'
                                download.save()
                                return  # Exit early for timed out downloads

                    if status == 'waiting_files_selection':
                        # Torrent needs file selection
                        self.stdout.write(f'  Torrent needs file selection, performing auto-selection')
                        if not self.dry_run:
                            if rd_service.auto_select_media_files(download.real_debrid_id):
                                self.stdout.write(f'  Successfully selected media files')
                            else:
                                self.stdout.write(f'  Failed to select media files')
                                download.status = 'failed'
                                download.error_message = 'Failed to select media files from torrent'
                                download.save()
                                return

                    elif status == 'downloaded':
                        # Torrent is complete, get download links
                        links = rd_service.get_download_links(download.real_debrid_id)

                        if not self.dry_run:
                            download.status = 'monitoring'
                            download.real_debrid_links = links
                            download.cache_progress = 100
                            download.save()

                        self.stdout.write(
                            self.style.SUCCESS(f'  Download completed! Now monitoring for Overseerr availability.')
                        )

                    elif status in ['error', 'virus', 'dead']:
                        # Download failed
                        if not self.dry_run:
                            download.status = 'failed'
                            download.error_message = f'Real Debrid error: {status}'
                            download.save()

                        self.stdout.write(
                            self.style.ERROR(f'  Download failed with status: {status}')
                        )

                    elif status == 'downloading':
                        # Still downloading
                        if not self.dry_run and download.status != 'downloading':
                            download.status = 'downloading'
                            download.save()
                else:
                    self.stdout.write(
                        self.style.WARNING(f'  Could not get torrent info from Real Debrid')
                    )

        elif download.status == 'monitoring':
            # Check if episode is now available in Plex
            tv_show = episode.season.tv_show
            episode_available = False
            quality_info = None

            # Method 1: Try Overseerr integration first (includes Plex data if configured)
            if sync_service:
                try:
                    self.stdout.write(f'  Checking availability via Overseerr...')

                    # Get fresh data from Overseerr
                    tv_details = sync_service.client.get_tv_show_details(tv_show.tmdb_id)

                    # Check for Plex rating key (either from show model or Overseerr response)
                    plex_rating_key = tv_show.plex_rating_key or tv_details.get('plexRatingKey')

                    if plex_rating_key:
                        # Update the show's rating key if it was missing
                        if not tv_show.plex_rating_key and tv_details.get('plexRatingKey'):
                            if not self.dry_run:
                                tv_show.plex_rating_key = tv_details['plexRatingKey']
                                tv_show.save()
                            self.stdout.write(f'  Updated Plex rating key for {tv_show.title}: {tv_details["plexRatingKey"]}')

                        # Check if this specific episode is now available
                        episode_found = False
                        for season_data in tv_details.get('seasons', []):
                            if season_data.get('seasonNumber') == episode.season.season_number:
                                for episode_data in season_data.get('episodes', []):
                                    if episode_data.get('episodeNumber') == episode.episode_number:
                                        episode_found = True
                                        # Check if episode has quality info (indicates availability)
                                        has_quality = episode_data.get('quality_info') is not None

                                        self.stdout.write(f'  Episode has quality info from Overseerr: {has_quality}')

                                        if has_quality:
                                            episode_available = True
                                            quality_info = episode_data
                                            self.stdout.write(f'  Episode found via Overseerr with quality data!')
                                        break
                                break

                        if episode_found and not episode_available:
                            self.stdout.write(f'  Episode found in Overseerr but no quality info yet')
                        elif not episode_found:
                            self.stdout.write(f'  Episode not found in Overseerr data')
                    else:
                        self.stdout.write(f'  No Plex rating key available (show: {tv_show.plex_rating_key}, overseerr: {tv_details.get("plexRatingKey")})')

                except Exception as e:
                    self.stdout.write(
                        self.style.WARNING(f'  Error checking Overseerr availability: {e}')
                    )

            # Method 2: Try direct Plex API if Overseerr didn't find the episode or if no Overseerr
            if not episode_available and plex_client and tv_show.plex_rating_key:
                try:
                    self.stdout.write(f'  Checking availability via direct Plex API...')

                    # Get quality info directly from Plex
                    plex_quality_info = plex_client.get_episode_quality_info(
                        tv_show.plex_rating_key,
                        episode.season.season_number,
                        episode.episode_number
                    )

                    if plex_quality_info:
                        episode_available = True
                        # Convert Plex quality info to episode_data format
                        quality_info = {
                            'quality_info': plex_quality_info,
                            'quality_level': plex_quality_info.get('quality_level', 'Unknown'),
                            'resolution': plex_quality_info.get('resolution'),
                            'video_codec': plex_quality_info.get('video_codec'),
                            'file_size': plex_quality_info.get('file_size')
                        }
                        self.stdout.write(f'  Episode found via direct Plex API with quality: {plex_quality_info.get("quality_level", "Unknown")}')
                    else:
                        self.stdout.write(f'  Episode not yet available in Plex')

                except Exception as e:
                    self.stdout.write(
                        self.style.WARNING(f'  Error checking direct Plex availability: {e}')
                    )

            # Update episode status if available
            if episode_available and quality_info:
                if not self.dry_run:
                    download.status = 'completed'
                    download.completed_at = timezone.now()

                    # Clean up old download if this was an upgrade
                    if download.cleanup_after_completion:
                        self._cleanup_specific_download(download.cleanup_after_completion, download)
                        # Clear the cleanup flag
                        download.cleanup_after_completion = ''

                    download.save()

                    # Update episode quality and check for quality upgrades
                    old_quality = episode.quality
                    self._update_episode_quality(episode, quality_info)
                    episode.save()

                    # Check if we need to clean up old downloads (for legacy cases)
                    self._cleanup_old_downloads_if_upgraded(episode, old_quality)

                self.stdout.write(
                    self.style.SUCCESS(f'  Episode is now available in Plex!')
                )
            elif not sync_service and not plex_client:
                self.stdout.write(f'  Neither Overseerr nor Plex is configured, cannot check availability')
            else:
                self.stdout.write(f'  Episode not yet available, continuing to monitor...')

    def _update_episode_quality(self, episode, episode_data):
        """Update episode quality based on Overseerr/Plex data"""
        try:
            from media_requests.models import MediaQuality

            # Extract quality information from episode data
            quality_info = episode_data.get('quality_info', {})
            quality_level = episode_data.get('quality_level', '')
            resolution = episode_data.get('resolution', '')
            video_codec = episode_data.get('video_codec', '')

            # Use quality_info if available (most detailed)
            file_size = 0
            if isinstance(quality_info, dict):
                quality_level = quality_info.get('quality_level', quality_level)
                video_codec = quality_info.get('video_codec', video_codec)
                resolution = quality_info.get('resolution', resolution)
                container = quality_info.get('container', '')
                file_size = quality_info.get('file_size', 0)

            # Fallback to direct fields
            if not quality_level:
                quality_level = episode_data.get('quality_level', 'Unknown')
            if not video_codec:
                video_codec = episode_data.get('video_codec', 'Unknown')
            if not resolution:
                resolution = episode_data.get('resolution', '')

            # Determine source based on various indicators
            source = 'Unknown'

            # Check download status for source info (from original torrent)
            download_status = getattr(episode, 'download_status', None)
            if download_status and download_status.torrent_title:
                torrent_title = download_status.torrent_title.upper()
                if 'BLURAY' in torrent_title or 'BLU-RAY' in torrent_title:
                    source = 'BluRay'
                elif 'WEB-DL' in torrent_title or 'WEBDL' in torrent_title:
                    source = 'WEB-DL'
                elif 'WEBRIP' in torrent_title or 'WEB-RIP' in torrent_title:
                    source = 'WEBRip'
                elif 'WEB' in torrent_title:
                    source = 'WEB'
                elif 'HDTV' in torrent_title:
                    source = 'HDTV'
                elif 'DVD' in torrent_title:
                    source = 'DVD'

            # Normalize video codec
            if video_codec:
                video_codec_lower = video_codec.lower()
                if video_codec_lower in ['h264', 'avc']:
                    video_codec = 'h264'
                elif video_codec_lower in ['h265', 'hevc']:
                    video_codec = 'h265'
                elif video_codec_lower in ['av1']:
                    video_codec = 'AV1'
                elif video_codec_lower in ['mpeg2']:
                    video_codec = 'MPEG2'

            # Log the extracted information
            self.stdout.write(f'    Extracted quality info:')
            self.stdout.write(f'      Quality Level: {quality_level}')
            self.stdout.write(f'      Source: {source}')
            self.stdout.write(f'      Video Codec: {video_codec}')
            self.stdout.write(f'      Resolution: {resolution}')
            if file_size:
                file_size_mb = file_size / (1024 * 1024)
                self.stdout.write(f'      File Size: {file_size_mb:.1f} MB')

            # Create or get MediaQuality object
            quality_obj, created = MediaQuality.objects.get_or_create(
                resolution=quality_level,
                source=source,
                codec=video_codec,
                defaults={}
            )

            # Update episode quality
            old_quality = episode.quality
            episode.quality = quality_obj

            if created:
                self.stdout.write(f'    Created new quality: {quality_level} {source} {video_codec}')

            if old_quality != quality_obj:
                self.stdout.write(f'    Updated episode quality: {old_quality} → {quality_obj}')
            else:
                self.stdout.write(f'    Episode quality unchanged: {quality_obj}')

        except Exception as e:
            self.stdout.write(
                self.style.WARNING(f'    Failed to update episode quality: {e}')
            )

    def _cleanup_specific_download(self, real_debrid_id, current_download):
        """Clean up a specific Real Debrid download by ID"""
        try:
            from media_requests.real_debrid_service import RealDebridService

            self.stdout.write(f'    Cleaning up old download: {real_debrid_id}')

            if not self.dry_run:
                rd_service = RealDebridService()
                success = rd_service.delete_torrent(real_debrid_id)
                if success:
                    self.stdout.write(f'    Successfully cleaned up old Real Debrid download: {real_debrid_id}')
                else:
                    self.stdout.write(f'    Failed to clean up old Real Debrid download: {real_debrid_id}')
            else:
                self.stdout.write(f'    Would clean up old Real Debrid download: {real_debrid_id} (dry run)')

        except Exception as e:
            self.stdout.write(
                self.style.WARNING(f'    Failed to cleanup specific download {real_debrid_id}: {e}')
            )

    def _cleanup_old_downloads_if_upgraded(self, episode, old_quality):
        """Clean up old Real Debrid downloads if the episode quality was upgraded"""
        try:
            new_quality = episode.quality

            # Only proceed if we have both old and new quality, and new is better
            if not old_quality or not new_quality:
                return

            # Check if this is a manual download - if so, always allow upgrade
            download_status = getattr(episode, 'download_status', None)
            is_manual_download = download_status and download_status.source == 'manual'

            if is_manual_download:
                # For manual downloads, only require basic quality improvement (not "significantly better")
                if not new_quality.is_better_than(old_quality):
                    self.stdout.write(f'    Manual download quality not better than existing, keeping existing downloads')
                    return
                self.stdout.write(f'    Manual download detected - allowing quality upgrade from {old_quality} to {new_quality}')
            else:
                # For automatic downloads, use profile rules
                quality_profile = episode.season.tv_show.quality_profile if hasattr(episode.season.tv_show, 'quality_profile') else None
                if not new_quality.is_significantly_better_than(old_quality, quality_profile):
                    self.stdout.write(f'    Quality not significantly upgraded per profile rules, keeping existing downloads')
                    return

            self.stdout.write(f'    Quality upgraded from {old_quality} to {new_quality}')

            # Look for other completed downloads for this episode that might be lower quality
            from media_requests.models import DownloadStatus

            # Get all completed downloads for this episode (there might be historical ones)
            # Exclude the current download to avoid deleting the newly added episode
            current_download = getattr(episode, 'download_status', None)
            old_downloads = DownloadStatus.objects.filter(
                episode=episode,
                status='completed'
            ).exclude(
                id=current_download.id if current_download else None
            )

            cleaned_count = 0
            for old_download in old_downloads:
                # Get the quality of this old download
                old_download_quality = old_download.get_quality_object()

                # Determine if we should clean up this old download
                should_cleanup = False
                if old_download_quality:
                    # Get app settings
                    from media_requests.models import AppSettings
                    app_settings = AppSettings.get_settings()

                    if is_manual_download:
                        # For manual downloads, check settings
                        should_cleanup = app_settings.manual_download_always_cleanup
                        if should_cleanup:
                            comparison_type = "manual download rules (user choice)"
                        else:
                            # If setting is disabled, use quality comparison
                            should_cleanup = new_quality.is_better_than(old_download_quality)
                            comparison_type = "manual download quality rules"
                    else:
                        # For automatic downloads, use profile rules
                        should_cleanup = new_quality.is_significantly_better_than(old_download_quality, quality_profile)
                        comparison_type = "profile rules"

                if should_cleanup:
                    self.stdout.write(f'    Cleaning up old download: {old_download.torrent_title} ({old_download_quality})')

                    if not self.dry_run:
                        # Try Real Debrid cleanup first
                        success = old_download.cleanup_real_debrid()
                        if success:
                            cleaned_count += 1
                            self.stdout.write(f'      Successfully removed from Real Debrid')
                        else:
                            # If Real Debrid cleanup failed or no Real Debrid ID, try Plex cleanup
                            if app_settings.enable_plex_cleanup_fallback:
                                self.stdout.write(f'      Real Debrid cleanup failed, trying Plex cleanup...')
                                plex_success = old_download.cleanup_via_plex()
                                if plex_success:
                                    cleaned_count += 1
                                    self.stdout.write(f'      Successfully removed via Plex')
                                else:
                                    self.stdout.write(f'      Failed to remove via both Real Debrid and Plex')
                            else:
                                self.stdout.write(f'      Real Debrid cleanup failed, Plex cleanup disabled in settings')
                    else:
                        cleaned_count += 1
                        if old_download.real_debrid_id:
                            self.stdout.write(f'      Would remove from Real Debrid (dry run)')
                        else:
                            self.stdout.write(f'      Would remove via Plex (dry run)')
                else:
                    self.stdout.write(f'    Keeping download: {old_download.torrent_title} (quality not significantly different per {comparison_type})')

            if cleaned_count > 0:
                self.stdout.write(
                    self.style.SUCCESS(f'    Cleaned up {cleaned_count} old download(s) from Real Debrid')
                )
            else:
                self.stdout.write(f'    No old downloads needed cleanup')

        except Exception as e:
            self.stdout.write(
                self.style.WARNING(f'    Failed to cleanup old downloads: {e}')
            )
