"""
Management command for monitoring Jackett RSS feeds
"""

import logging
from django.core.management.base import BaseCommand, CommandError
from django.conf import settings

from media_requests.rss_service import RSSMonitoringService

logger = logging.getLogger(__name__)


class Command(BaseCommand):
    help = 'Monitor Jackett RSS feeds for new torrents and automatically download qualifying ones'

    def add_arguments(self, parser):
        parser.add_argument(
            '--hours',
            type=int,
            default=24,
            help='Number of hours to look back in RSS feed (default: 24)'
        )
        parser.add_argument(
            '--dry-run',
            action='store_true',
            help='Show what would be downloaded without actually downloading'
        )
        parser.add_argument(
            '--once',
            action='store_true',
            help='Run once and exit (default is to run continuously)'
        )

    def handle(self, *args, **options):
        self.dry_run = options['dry_run']
        self.hours = options['hours']
        self.run_once = options['once']

        if self.dry_run:
            self.stdout.write(
                self.style.WARNING('DRY RUN MODE - No downloads will be started')
            )

        # Check if APIs are configured
        if not settings.JACKETT_API_KEY:
            raise CommandError(
                'JACKETT_API_KEY is not configured. Please set it in your .env file.'
            )

        if not settings.JACKETT_BASE_URL:
            raise CommandError(
                'JACKETT_BASE_URL is not configured. Please set it in your .env file.'
            )

        if not settings.REAL_DEBRID_API_KEY:
            raise CommandError(
                'REAL_DEBRID_API_KEY is not configured. Please set it in your .env file.'
            )

        self.stdout.write(
            self.style.SUCCESS(f'Starting RSS monitoring (looking back {self.hours} hours)...')
        )

        try:
            rss_service = RSSMonitoringService()

            if self.run_once:
                self._monitor_cycle(rss_service)
            else:
                # Run continuously
                import time
                while True:
                    try:
                        self._monitor_cycle(rss_service)

                        # Wait 15 minutes before next check
                        self.stdout.write('Waiting 15 minutes before next RSS check...')
                        time.sleep(900)  # 15 minutes

                    except KeyboardInterrupt:
                        self.stdout.write(
                            self.style.SUCCESS('RSS monitoring stopped by user.')
                        )
                        break
                    except Exception as e:
                        self.stdout.write(
                            self.style.ERROR(f'Error in RSS monitoring cycle: {e}')
                        )
                        # Wait a bit before retrying
                        time.sleep(300)  # 5 minutes

        except Exception as e:
            self.stdout.write(
                self.style.ERROR(f'RSS monitoring failed: {e}')
            )

    def _monitor_cycle(self, rss_service):
        """Run one RSS monitoring cycle"""
        self.stdout.write('Starting RSS monitoring cycle...')

        if self.dry_run:
            # For dry run, we'll need to implement a separate method
            # that shows what would be downloaded without actually doing it
            self.stdout.write(
                self.style.WARNING('Dry run mode not fully implemented yet')
            )
            return

        # Run RSS monitoring
        stats = rss_service.run_rss_monitoring(hours=self.hours)

        # Display results
        if 'error' in stats:
            self.stdout.write(
                self.style.ERROR(f'RSS monitoring failed: {stats["error"]}')
            )
        else:
            self.stdout.write(
                self.style.SUCCESS(
                    f'RSS monitoring completed:\n'
                    f'  Total RSS items: {stats.get("total_items", 0)}\n'
                    f'  Matched episodes: {stats.get("matched_episodes", 0)}\n'
                    f'  Downloads started: {stats.get("downloads_started", 0)}\n'
                    f'  Downloads failed: {stats.get("downloads_failed", 0)}\n'
                    f'  Skipped: {stats.get("skipped", 0)}'
                )
            )
