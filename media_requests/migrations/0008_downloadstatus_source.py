# Generated by Django 4.2.23 on 2025-07-31 14:26

from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('media_requests', '0007_downloadstatus_cleanup_after_completion'),
    ]

    operations = [
        migrations.AddField(
            model_name='downloadstatus',
            name='source',
            field=models.CharField(choices=[('manual', 'Manual Search'), ('auto', 'Automatic Search')], default='auto', help_text='How this download was initiated', max_length=10),
        ),
    ]
