# Generated by Django 4.2.23 on 2025-07-31 15:10

import django.core.validators
from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('media_requests', '0008_downloadstatus_source'),
    ]

    operations = [
        migrations.CreateModel(
            name='AppSettings',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('manual_download_always_cleanup', models.BooleanField(default=True, help_text='Always cleanup old downloads when user manually downloads a replacement, regardless of quality')),
                ('enable_plex_cleanup_fallback', models.BooleanField(default=True, help_text='Enable Plex API cleanup as fallback when Real Debrid cleanup fails')),
                ('auto_cleanup_quality_threshold', models.IntegerField(default=100, help_text='Quality score threshold for automatic cleanup (0-1000)', validators=[django.core.validators.MinValueValidator(0), django.core.validators.MaxValueValidator(1000)])),
                ('monitor_downloads_enabled', models.BooleanField(default=True, help_text='Enable automatic download monitoring')),
                ('monitor_interval_minutes', models.IntegerField(default=5, help_text='Download monitoring interval in minutes', validators=[django.core.validators.MinValueValidator(1), django.core.validators.MaxValueValidator(60)])),
                ('jackett_search_timeout', models.IntegerField(default=30, help_text='Jackett search timeout in seconds', validators=[django.core.validators.MinValueValidator(5), django.core.validators.MaxValueValidator(120)])),
                ('max_search_results', models.IntegerField(default=50, help_text='Maximum number of search results to return', validators=[django.core.validators.MinValueValidator(10), django.core.validators.MaxValueValidator(200)])),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
            ],
            options={
                'verbose_name': 'Application Settings',
                'verbose_name_plural': 'Application Settings',
            },
        ),
    ]
