# Generated by Django 4.2.23 on 2025-07-31 18:42

import django.core.validators
from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('media_requests', '0009_appsettings'),
    ]

    operations = [
        migrations.AddField(
            model_name='appsettings',
            name='real_debrid_cache_timeout_hours',
            field=models.IntegerField(default=24, help_text='Maximum hours to wait for Real Debrid caching before giving up (1-168 hours)', validators=[django.core.validators.MinValueValidator(1), django.core.validators.MaxValueValidator(168)]),
        ),
        migrations.AddField(
            model_name='appsettings',
            name='wait_for_real_debrid_cache',
            field=models.BooleanField(default=False, help_text='Wait for Real Debrid to cache torrents instead of downloading immediately'),
        ),
        migrations.AddField(
            model_name='downloadstatus',
            name='cache_check_started_at',
            field=models.DateTimeField(blank=True, null=True),
        ),
        migrations.AddField(
            model_name='downloadstatus',
            name='cache_progress',
            field=models.IntegerField(default=0, help_text='Cache progress percentage (0-100)'),
        ),
        migrations.AlterField(
            model_name='downloadstatus',
            name='status',
            field=models.CharField(choices=[('searching', 'Searching'), ('pending', 'Pending'), ('caching', 'Caching'), ('downloading', 'Downloading'), ('completed', 'Completed'), ('failed', 'Failed'), ('monitoring', 'Monitoring')], default='searching', max_length=20),
        ),
    ]
