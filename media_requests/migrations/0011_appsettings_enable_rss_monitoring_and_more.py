# Generated by Django 4.2.23 on 2025-07-31 19:39

import django.core.validators
from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('media_requests', '0010_appsettings_real_debrid_cache_timeout_hours_and_more'),
    ]

    operations = [
        migrations.AddField(
            model_name='appsettings',
            name='enable_rss_monitoring',
            field=models.BooleanField(default=False, help_text='Enable automatic monitoring of Jackett RSS feeds for new torrents'),
        ),
        migrations.AddField(
            model_name='appsettings',
            name='rss_monitoring_hours',
            field=models.IntegerField(default=24, help_text='Hours to look back in RSS feed for new torrents (1-168 hours)', validators=[django.core.validators.MinValueValidator(1), django.core.validators.MaxValueValidator(168)]),
        ),
        migrations.AlterField(
            model_name='appsettings',
            name='wait_for_real_debrid_cache',
            field=models.BooleanField(default=False, help_text='Wait for Real Debrid to cache torrents instead of downloading immediately (manual searches only)'),
        ),
    ]
