# Generated migration to assign default profile to existing shows

from django.db import migrations


def assign_default_profile_to_shows(apps, schema_editor):
    """Assign default quality profile to shows that don't have one"""
    TVShow = apps.get_model('media_requests', 'TVShow')
    QualityProfile = apps.get_model('media_requests', 'QualityProfile')
    
    # Get the default profile
    try:
        default_profile = QualityProfile.objects.get(is_default=True, is_active=True)
    except QualityProfile.DoesNotExist:
        # If no default profile exists, try to find any active profile
        try:
            default_profile = QualityProfile.objects.filter(is_active=True).first()
            if default_profile:
                default_profile.is_default = True
                default_profile.save()
        except:
            # If no profiles exist at all, skip this migration
            return
    
    if default_profile:
        # Update all shows without a quality profile
        shows_without_profile = TVShow.objects.filter(quality_profile__isnull=True)
        shows_without_profile.update(quality_profile=default_profile)
        
        print(f"Assigned default profile '{default_profile.name}' to {shows_without_profile.count()} shows")


def reverse_assign_default_profile(apps, schema_editor):
    """Reverse migration - this is not reversible as we don't know which shows originally had no profile"""
    pass


class Migration(migrations.Migration):

    dependencies = [
        ('media_requests', '0011_appsettings_enable_rss_monitoring_and_more'),
    ]

    operations = [
        migrations.RunPython(
            assign_default_profile_to_shows,
            reverse_assign_default_profile,
        ),
    ]
