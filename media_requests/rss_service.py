"""
RSS Feed Service for monitoring Jackett RSS feeds for new torrents
"""

import logging
import re
import requests
import xml.etree.ElementTree as ET
from datetime import datetime, timedelta
from typing import List, Dict, Optional, Tuple
from urllib.parse import urljoin, parse_qs, urlparse
from django.conf import settings
from django.utils import timezone

from .jackett_service import JackettService, JackettSearchResult
from .models import TVShow, Episode, DownloadStatus, MediaQuality, QualityProfile
from .real_debrid_service import RealDebridService, RealDebridAPIError

logger = logging.getLogger(__name__)


class RSSFeedItem:
    """Represents a single item from the RSS feed"""
    
    def __init__(self, data: Dict):
        self.title = data.get('title', '')
        self.link = data.get('link', '')
        self.guid = data.get('guid', '')
        self.pub_date = data.get('pub_date')
        self.description = data.get('description', '')
        self.size = data.get('size', 0)
        self.seeders = data.get('seeders', 0)
        self.peers = data.get('peers', 0)
        self.indexer = data.get('indexer', 'Unknown')
        self.category = data.get('category', '')
        self.download_url = data.get('download_url', '')
        self.magnet_url = data.get('magnet_url', '')
        
        # Parse quality and other metadata from title
        self.quality = self._parse_quality(self.title)
        self.show_title, self.season, self.episode = self._parse_episode_info(self.title)
    
    def _parse_quality(self, title: str) -> str:
        """Parse quality information from title"""
        title_upper = title.upper()
        
        if '2160P' in title_upper or '4K' in title_upper or 'UHD' in title_upper:
            return '4K'
        elif '1080P' in title_upper or 'FHD' in title_upper:
            return '1080p'
        elif '720P' in title_upper or 'HD' in title_upper:
            return '720p'
        elif '480P' in title_upper or 'SD' in title_upper:
            return '480p'
        else:
            return 'Unknown'
    
    def _parse_episode_info(self, title: str) -> Tuple[Optional[str], Optional[int], Optional[int]]:
        """Parse show title, season, and episode from torrent title"""
        # Common patterns for TV episodes
        patterns = [
            # S01E01, S1E1 format
            r'(.+?)[.\s]+S(\d{1,2})E(\d{1,2})',
            # 1x01, 01x01 format  
            r'(.+?)[.\s]+(\d{1,2})x(\d{1,2})',
            # Season 1 Episode 1 format
            r'(.+?)[.\s]+Season[.\s]+(\d{1,2})[.\s]+Episode[.\s]+(\d{1,2})',
        ]
        
        for pattern in patterns:
            match = re.search(pattern, title, re.IGNORECASE)
            if match:
                show_title = match.group(1).strip()
                # Clean up show title
                show_title = re.sub(r'[.\-_]+', ' ', show_title).strip()
                season = int(match.group(2))
                episode = int(match.group(3))
                return show_title, season, episode
        
        return None, None, None
    
    def to_jackett_result(self) -> JackettSearchResult:
        """Convert to JackettSearchResult for compatibility with existing code"""
        data = {
            'title': self.title,
            'link': self.link,
            'guid': self.guid,
            'size': self.size,
            'seeders': self.seeders,
            'peers': self.peers,
            'indexer': self.indexer,
            'category': self.category,
            'downloadUrl': self.download_url,  # JackettSearchResult expects camelCase
            'magnetUrl': self.magnet_url,      # JackettSearchResult expects camelCase
        }
        return JackettSearchResult(data)


class JackettRSSService:
    """Service for monitoring Jackett RSS feeds"""
    
    def __init__(self):
        self.jackett_service = JackettService()
        self.base_url = self.jackett_service.base_url
        self.api_key = self.jackett_service.api_key
        self.session = requests.Session()
        self.session.timeout = 30
    
    def get_rss_feed_url(self, indexer: str = 'all', category: str = '5000', hours: int = 24) -> str:
        """Get RSS feed URL for Jackett"""
        if indexer == 'all':
            endpoint = '/api/v2.0/indexers/all/results/torznab/'
        else:
            endpoint = f'/api/v2.0/indexers/{indexer}/results/torznab/'
        
        url = urljoin(self.base_url, endpoint)
        
        # Add RSS parameters
        params = {
            'apikey': self.api_key,
            't': 'search',
            'cat': category,
            'limit': '100',  # Get more results for RSS
        }
        
        # Build URL with parameters
        param_string = '&'.join([f"{k}={v}" for k, v in params.items()])
        return f"{url}?{param_string}"
    
    def fetch_rss_feed(self, hours: int = 24) -> List[RSSFeedItem]:
        """Fetch and parse RSS feed from Jackett"""
        try:
            url = self.get_rss_feed_url(hours=hours)
            logger.info(f"Fetching Jackett RSS feed from last {hours} hours")
            
            response = self.session.get(url)
            response.raise_for_status()
            
            # Parse XML response
            items = self._parse_rss_xml(response.text)
            
            # Filter items by publication date (last X hours)
            cutoff_time = timezone.now() - timedelta(hours=hours)
            recent_items = []
            
            for item in items:
                if item.pub_date and item.pub_date >= cutoff_time:
                    recent_items.append(item)
            
            logger.info(f"Found {len(recent_items)} recent torrents from RSS feed")
            return recent_items
            
        except requests.exceptions.RequestException as e:
            logger.error(f"Failed to fetch RSS feed: {e}")
            return []
        except Exception as e:
            logger.error(f"Error processing RSS feed: {e}")
            return []
    
    def _parse_rss_xml(self, xml_content: str) -> List[RSSFeedItem]:
        """Parse RSS XML content and extract torrent items"""
        try:
            root = ET.fromstring(xml_content)
            items = []
            
            # Find all item elements
            for item_elem in root.findall('.//item'):
                item_data = {}
                
                # Extract basic fields
                item_data['title'] = self._get_element_text(item_elem, 'title')
                item_data['link'] = self._get_element_text(item_elem, 'link')
                item_data['guid'] = self._get_element_text(item_elem, 'guid')
                item_data['description'] = self._get_element_text(item_elem, 'description')
                
                # Parse publication date
                pub_date_str = self._get_element_text(item_elem, 'pubDate')
                if pub_date_str:
                    try:
                        # Parse RFC 2822 date format
                        pub_date = datetime.strptime(pub_date_str, '%a, %d %b %Y %H:%M:%S %z')
                        item_data['pub_date'] = pub_date
                    except ValueError:
                        try:
                            # Try alternative format
                            pub_date = datetime.strptime(pub_date_str, '%a, %d %b %Y %H:%M:%S GMT')
                            item_data['pub_date'] = pub_date.replace(tzinfo=timezone.utc)
                        except ValueError:
                            logger.warning(f"Could not parse date: {pub_date_str}")
                            item_data['pub_date'] = None
                
                # Extract torznab attributes
                for attr in item_elem.findall('.//{http://torznab.com/schemas/2015/feed}attr'):
                    name = attr.get('name', '')
                    value = attr.get('value', '')
                    
                    if name == 'size':
                        try:
                            item_data['size'] = int(value)
                        except (ValueError, TypeError):
                            item_data['size'] = 0
                    elif name == 'seeders':
                        try:
                            item_data['seeders'] = int(value)
                        except (ValueError, TypeError):
                            item_data['seeders'] = 0
                    elif name == 'peers':
                        try:
                            item_data['peers'] = int(value)
                        except (ValueError, TypeError):
                            item_data['peers'] = 0
                    elif name == 'downloadvolumefactor':
                        item_data['download_volume_factor'] = value
                    elif name == 'uploadvolumefactor':
                        item_data['upload_volume_factor'] = value
                    elif name == 'magneturl':
                        item_data['magnet_url'] = value
                
                # Extract indexer from jackettindexer element
                jackett_indexer = item_elem.find('.//{http://torznab.com/schemas/2015/feed}jackettindexer')
                if jackett_indexer is not None and jackett_indexer.text:
                    item_data['indexer'] = jackett_indexer.text
                else:
                    item_data['indexer'] = 'Unknown'
                
                # Use link as download URL if no magnet
                if not item_data.get('magnet_url') and item_data.get('link'):
                    item_data['download_url'] = item_data['link']

                # Only add items that have episode information AND valid URLs
                rss_item = RSSFeedItem(item_data)
                if (rss_item.show_title and rss_item.season is not None and rss_item.episode is not None and
                    (rss_item.magnet_url or rss_item.download_url)):
                    items.append(rss_item)
                elif rss_item.show_title and rss_item.season is not None and rss_item.episode is not None:
                    logger.debug(f"Skipping RSS item with no valid URL: {rss_item.title[:100]}")
            
            return items
            
        except ET.ParseError as e:
            logger.error(f"Failed to parse RSS XML: {e}")
            return []
    
    def _get_element_text(self, parent, tag_name: str) -> str:
        """Safely get text content of an XML element"""
        element = parent.find(tag_name)
        return element.text if element is not None and element.text else ''

    def find_matching_episode(self, rss_item: RSSFeedItem) -> Optional[Episode]:
        """Find matching episode in our database"""
        if not rss_item.show_title or rss_item.season is None or rss_item.episode is None:
            return None

        try:
            # Try to find matching TV show (case-insensitive, fuzzy matching)
            tv_shows = TVShow.objects.filter(title__icontains=rss_item.show_title)

            # If no exact match, try partial matching
            if not tv_shows.exists():
                # Try matching individual words
                words = rss_item.show_title.split()
                for word in words:
                    if len(word) > 3:  # Only use significant words
                        tv_shows = TVShow.objects.filter(title__icontains=word)
                        if tv_shows.exists():
                            break

            if not tv_shows.exists():
                logger.debug(f"No matching TV show found for: {rss_item.show_title}")
                return None

            # If multiple shows match, try to find the best one
            best_show = None
            best_score = 0

            for show in tv_shows:
                # Calculate similarity score (simple word matching)
                score = self._calculate_title_similarity(rss_item.show_title, show.title)
                if score > best_score:
                    best_score = score
                    best_show = show

            if not best_show:
                return None

            # Find the specific episode
            try:
                episode = Episode.objects.get(
                    season__tv_show=best_show,
                    season__season_number=rss_item.season,
                    episode_number=rss_item.episode
                )
                logger.debug(f"Found matching episode: {best_show.title} S{rss_item.season:02d}E{rss_item.episode:02d}")
                return episode
            except Episode.DoesNotExist:
                logger.debug(f"Episode not found: {best_show.title} S{rss_item.season:02d}E{rss_item.episode:02d}")
                return None

        except Exception as e:
            logger.error(f"Error finding matching episode: {e}")
            return None

    def _calculate_title_similarity(self, title1: str, title2: str) -> float:
        """Calculate similarity score between two titles"""
        # Simple word-based similarity
        words1 = set(title1.lower().split())
        words2 = set(title2.lower().split())

        if not words1 or not words2:
            return 0.0

        intersection = words1.intersection(words2)
        union = words1.union(words2)

        return len(intersection) / len(union) if union else 0.0

    def should_download_torrent(self, rss_item: RSSFeedItem, episode: Episode) -> Tuple[bool, str]:
        """Determine if we should download this torrent based on quality profile and existing downloads"""
        try:
            # Check if we have a valid URL first
            torrent_url = rss_item.magnet_url if rss_item.magnet_url else rss_item.download_url
            if not torrent_url or not torrent_url.strip():
                return False, "No valid download URL"

            if not (torrent_url.startswith('magnet:') or torrent_url.startswith('http')):
                return False, "Invalid URL format"

            # Get quality profile for the show
            quality_profile = episode.season.tv_show.quality_profile
            if not quality_profile:
                # Use default profile if available
                quality_profile = QualityProfile.objects.filter(is_default=True).first()

            if not quality_profile:
                logger.debug(f"No quality profile found for {episode.season.tv_show.title}")
                return False, "No quality profile configured"

            # Convert RSS item to JackettSearchResult for compatibility
            jackett_result = rss_item.to_jackett_result()

            # Calculate score using existing quality profile logic
            score = self.jackett_service.calculate_torrent_score(jackett_result, quality_profile)

            # Check if quality meets minimum requirements
            if score < quality_profile.minimum_custom_format_score:
                return False, f"Quality score {score} below minimum {quality_profile.minimum_custom_format_score}"

            # Check if we already have this episode
            existing_download = DownloadStatus.objects.filter(
                episode=episode,
                status__in=['completed', 'monitoring', 'downloading', 'pending', 'caching']
            ).first()

            if not existing_download:
                # No existing download, check if quality is allowed
                if self._is_quality_allowed(jackett_result, quality_profile):
                    return True, "New episode, quality allowed"
                else:
                    return False, "Quality not allowed in profile"

            # We have an existing download, check if this is an upgrade
            if not quality_profile.allow_upgrades:
                return False, "Upgrades disabled in profile"

            # Get existing quality
            existing_quality = existing_download.get_quality_object()
            if not existing_quality:
                # If we can't determine existing quality, allow download
                return True, "Cannot determine existing quality, allowing download"

            # Get new quality
            new_quality = self._get_quality_from_jackett_result(jackett_result)
            if not new_quality:
                return False, "Cannot determine new torrent quality"

            # Check if this is a significant upgrade
            if new_quality.is_significantly_better_than(existing_quality, quality_profile):
                # Check if we've reached the upgrade limit
                if quality_profile.upgrade_until_quality:
                    if new_quality._matches_quality_definition(existing_quality, quality_profile.upgrade_until_quality):
                        return False, "Already reached upgrade until quality"

                return True, f"Quality upgrade: {existing_quality} → {new_quality}"
            else:
                return False, f"Not a significant upgrade: {existing_quality} vs {new_quality}"

        except Exception as e:
            logger.error(f"Error evaluating torrent for download: {e}")
            return False, f"Error evaluating torrent: {str(e)}"

    def _is_quality_allowed(self, jackett_result: JackettSearchResult, quality_profile: QualityProfile) -> bool:
        """Check if quality is allowed in the profile"""
        try:
            allowed_qualities = [
                item.quality.resolution for item in quality_profile.quality_items.filter(allowed=True)
            ]
            return jackett_result.quality in allowed_qualities or jackett_result.quality == 'Unknown'
        except Exception:
            return True  # Default to allowing if we can't determine

    def _get_quality_from_jackett_result(self, jackett_result: JackettSearchResult) -> Optional[MediaQuality]:
        """Convert JackettSearchResult to MediaQuality object"""
        try:
            # Determine source from title
            source = 'Unknown'
            title_upper = jackett_result.title.upper()
            if 'REMUX' in title_upper:
                source = 'REMUX'
            elif 'BLURAY' in title_upper or 'BLU-RAY' in title_upper:
                source = 'BluRay'
            elif 'WEB-DL' in title_upper or 'WEBDL' in title_upper:
                source = 'WEB-DL'
            elif 'WEBRIP' in title_upper or 'WEB-RIP' in title_upper:
                source = 'WEBRip'
            elif 'WEB' in title_upper:
                source = 'WEB'
            elif 'HDTV' in title_upper:
                source = 'HDTV'
            elif 'DVD' in title_upper:
                source = 'DVD'

            # Determine codec from title
            codec = 'Unknown'
            if 'AV1' in title_upper:
                codec = 'AV1'
            elif 'H265' in title_upper or 'HEVC' in title_upper or 'X265' in title_upper:
                codec = 'h265'
            elif 'H264' in title_upper or 'AVC' in title_upper or 'X264' in title_upper:
                codec = 'h264'

            quality_obj, _ = MediaQuality.objects.get_or_create(
                resolution=jackett_result.quality,
                source=source,
                codec=codec,
                defaults={}
            )
            return quality_obj
        except Exception as e:
            logger.error(f"Error creating quality object: {e}")
            return None

    def process_rss_torrents(self, hours: int = 24) -> Dict[str, int]:
        """Process RSS feed and automatically download qualifying torrents"""
        stats = {
            'total_items': 0,
            'matched_episodes': 0,
            'downloads_started': 0,
            'downloads_failed': 0,
            'skipped': 0
        }

        try:
            # Fetch RSS feed
            rss_items = self.fetch_rss_feed(hours=hours)
            stats['total_items'] = len(rss_items)

            if not rss_items:
                logger.info("No recent torrents found in RSS feed")
                return stats

            # Group RSS items by episode for intelligent processing
            episode_groups = self._group_rss_items_by_episode(rss_items)

            # Process each episode group
            for episode_key, releases in episode_groups.items():
                try:
                    # Sort releases by quality (highest first)
                    sorted_releases = self._sort_releases_by_quality(releases)

                    # Process releases for this episode intelligently
                    episode_stats = self._process_episode_releases(sorted_releases)

                    # Update overall stats
                    stats['matched_episodes'] += episode_stats['matched_episodes']
                    stats['downloads_started'] += episode_stats['downloads_started']
                    stats['downloads_failed'] += episode_stats['downloads_failed']
                    stats['skipped'] += episode_stats['skipped']

                except Exception as e:
                    logger.error(f"Error processing episode group {episode_key}: {e}")
                    stats['downloads_failed'] += len(releases)

            logger.info(f"RSS processing complete: {stats}")
            return stats

        except Exception as e:
            logger.error(f"Error processing RSS torrents: {e}")
            return stats

    def _group_rss_items_by_episode(self, rss_items: List[RSSFeedItem]) -> Dict[str, List[Tuple[RSSFeedItem, Episode]]]:
        """Group RSS items by episode (show + season + episode)"""
        episode_groups = {}

        for rss_item in rss_items:
            # Find matching episode
            episode = self.find_matching_episode(rss_item)
            if not episode:
                continue

            # Create episode key
            episode_key = f"{episode.season.tv_show.title}_S{episode.season.season_number:02d}E{episode.episode_number:02d}"

            if episode_key not in episode_groups:
                episode_groups[episode_key] = []

            episode_groups[episode_key].append((rss_item, episode))

        return episode_groups

    def _sort_releases_by_quality(self, releases: List[Tuple[RSSFeedItem, Episode]]) -> List[Tuple[RSSFeedItem, Episode]]:
        """Sort releases by quality (highest first) and then by seeders"""
        def get_quality_priority(rss_item: RSSFeedItem) -> int:
            """Get numeric priority for quality sorting (higher = better quality)"""
            quality_priorities = {
                '4K': 4,
                '1080p': 3,
                '720p': 2,
                '480p': 1,
                'Unknown': 0
            }
            return quality_priorities.get(rss_item.quality, 0)

        # Sort by quality (descending), then by seeders (descending)
        return sorted(releases, key=lambda x: (get_quality_priority(x[0]), x[0].seeders), reverse=True)

    def _process_episode_releases(self, sorted_releases: List[Tuple[RSSFeedItem, Episode]]) -> Dict[str, int]:
        """Process releases for a single episode intelligently"""
        stats = {
            'matched_episodes': 0,
            'downloads_started': 0,
            'downloads_failed': 0,
            'skipped': 0
        }

        if not sorted_releases:
            return stats

        # Get the episode from the first release (they're all for the same episode)
        episode = sorted_releases[0][1]
        stats['matched_episodes'] = 1

        logger.info(f"Processing {len(sorted_releases)} releases for {episode.season.tv_show.title} S{episode.season.season_number:02d}E{episode.episode_number:02d}")

        for i, (rss_item, episode) in enumerate(sorted_releases):
            try:
                # Check current episode status before processing each release
                episode_status = self._get_episode_status(episode)

                logger.debug(f"Release {i+1}/{len(sorted_releases)}: {rss_item.quality} - {rss_item.title[:60]}... (Status: {episode_status})")

                # Determine if we should try this release based on current status
                should_try, reason = self._should_try_release(rss_item, episode, episode_status, i == 0)

                if not should_try:
                    logger.debug(f"Skipping release: {reason}")
                    stats['skipped'] += 1
                    continue

                # Check if we should download this torrent (quality profile, etc.)
                should_download, download_reason = self.should_download_torrent(rss_item, episode)

                if not should_download:
                    logger.debug(f"Skipping {rss_item.title}: {download_reason}")
                    stats['skipped'] += 1
                    continue

                # Attempt to download
                success = self._download_torrent(rss_item, episode, f"{reason} - {download_reason}")
                if success:
                    stats['downloads_started'] += 1
                    logger.info(f"RSS auto-download started: {rss_item.title} - {reason}")
                    # Stop processing other releases for this episode since we got one
                    break
                else:
                    stats['downloads_failed'] += 1
                    # Continue to next release if this one failed

            except Exception as e:
                logger.error(f"Error processing release {rss_item.title}: {e}")
                stats['downloads_failed'] += 1

        return stats

    def _get_episode_status(self, episode: Episode) -> str:
        """Get current status of an episode's downloads"""
        from .models import DownloadStatus

        # Check for existing downloads
        existing_downloads = DownloadStatus.objects.filter(episode=episode).order_by('-started_at')

        if not existing_downloads.exists():
            return 'no_downloads'

        latest_download = existing_downloads.first()

        # Check for active downloads (downloading, pending, caching, monitoring)
        active_statuses = ['downloading', 'pending', 'caching', 'monitoring']
        if latest_download.status in active_statuses:
            return f'active_{latest_download.status}'

        # Check for completed downloads
        if latest_download.status == 'completed':
            return 'completed'

        # Check for failed downloads
        if latest_download.status in ['failed', 'error']:
            return 'failed'

        return latest_download.status

    def _should_try_release(self, rss_item: RSSFeedItem, episode: Episode, episode_status: str, is_highest_quality: bool) -> Tuple[bool, str]:
        """Determine if we should try this release based on episode status and release quality"""

        # If no downloads exist, always try the highest quality first
        if episode_status == 'no_downloads':
            if is_highest_quality:
                return True, "First release for episode"
            else:
                return False, "Lower quality release skipped (no active downloads to replace)"

        # If episode is already completed, only try if this is significantly better quality
        if episode_status == 'completed':
            # Get the quality of the completed download
            from .models import DownloadStatus
            completed_download = DownloadStatus.objects.filter(
                episode=episode,
                status='completed'
            ).order_by('-started_at').first()

            if completed_download and completed_download.quality:
                current_quality = completed_download.quality
                new_quality = rss_item.quality

                # Only upgrade if new quality is significantly better
                if self._is_quality_upgrade(current_quality, new_quality):
                    return True, f"Quality upgrade from {current_quality} to {new_quality}"
                else:
                    return False, f"Quality not better than existing {current_quality}"
            else:
                # If we can't determine current quality, allow highest quality releases
                if is_highest_quality:
                    return True, "Potential quality upgrade (unknown current quality)"
                else:
                    return False, "Lower quality release skipped (episode completed)"

        # If there's an active download, only try if it's failed or if this is much better quality
        if episode_status.startswith('active_'):
            current_status = episode_status.replace('active_', '')

            # If current download is failing/stuck, try alternatives
            if current_status in ['failed', 'error']:
                return True, f"Trying alternative (current download {current_status})"

            # If current download is active, only try if this is highest quality
            if is_highest_quality:
                # Check if current download is lower quality
                from .models import DownloadStatus
                active_download = DownloadStatus.objects.filter(
                    episode=episode,
                    status__in=['downloading', 'pending', 'caching', 'monitoring']
                ).order_by('-started_at').first()

                if active_download and active_download.quality:
                    if self._is_quality_upgrade(active_download.quality, rss_item.quality):
                        return True, f"Better quality than active download ({active_download.quality} -> {rss_item.quality})"

                return False, f"Active download in progress ({current_status})"
            else:
                return False, f"Lower quality release skipped (active download: {current_status})"

        # If previous downloads failed, try this release
        if episode_status == 'failed':
            return True, "Trying after previous failure"

        # Default: try the release
        return True, f"Trying release (status: {episode_status})"

    def _is_quality_upgrade(self, current_quality: str, new_quality: str) -> bool:
        """Check if new quality is a significant upgrade over current quality"""
        quality_hierarchy = {
            '480p': 1,
            '720p': 2,
            '1080p': 3,
            '4K': 4,
            'Unknown': 0
        }

        current_score = quality_hierarchy.get(current_quality, 0)
        new_score = quality_hierarchy.get(new_quality, 0)

        # Only consider it an upgrade if new quality is at least one level higher
        return new_score > current_score

    def _download_torrent(self, rss_item: RSSFeedItem, episode: Episode, reason: str) -> bool:
        """Download a torrent using the existing automatic download logic"""
        try:
            # Convert to JackettSearchResult for compatibility
            jackett_result = rss_item.to_jackett_result()

            # Initialize Real Debrid service
            rd_service = RealDebridService()

            # Validate that we have a valid URL
            torrent_url = jackett_result.magnet_url if jackett_result.magnet_url else jackett_result.download_url
            if not torrent_url or not torrent_url.strip():
                logger.error(f"RSS torrent has no valid URL: {jackett_result.title}")
                return False

            # Validate URL format
            if not (torrent_url.startswith('magnet:') or torrent_url.startswith('http')):
                logger.error(f"RSS torrent has invalid URL format: {torrent_url[:100]}")
                return False

            # Check if torrent is cached (only download cached torrents for RSS)
            is_cached, cache_info = rd_service.is_torrent_cached(torrent_url, jackett_result.title)

            if not is_cached:
                logger.debug(f"RSS torrent not cached, skipping: {jackett_result.title}")
                return False

            # Get the final magnet URL and torrent ID
            final_magnet_url = cache_info.get('magnet_url', jackett_result.magnet_url)
            torrent_id = cache_info.get('torrent_id')

            if not torrent_id:
                logger.error(f"No torrent ID returned for cached torrent: {jackett_result.title}")
                return False

            # Check if media files are already selected (from cache check)
            torrent_info = rd_service.get_torrent_info(torrent_id)
            if not torrent_info:
                logger.error(f"Failed to get torrent info for RSS torrent: {jackett_result.title}")
                return False

            # Only select media files if they haven't been selected yet
            if torrent_info.get('status') != 'downloaded':
                if not rd_service.auto_select_media_files(torrent_id):
                    logger.error(f"Failed to select media files for RSS torrent: {jackett_result.title}")
                    return False

            # Get download links
            links = rd_service.get_download_links(torrent_id)
            if not links:
                logger.error(f"Failed to get download links for RSS torrent: {jackett_result.title}")
                return False

            # Remove existing download if present (for upgrades)
            existing_download = DownloadStatus.objects.filter(
                episode=episode,
                status__in=['completed', 'monitoring', 'downloading', 'pending', 'caching']
            ).first()

            cleanup_after_completion = ''
            if existing_download:
                cleanup_after_completion = existing_download.real_debrid_id or ''
                logger.info(f"RSS upgrade: will cleanup existing download {cleanup_after_completion}")

            # Create download status record
            download_status = DownloadStatus.objects.create(
                episode=episode,
                torrent_title=jackett_result.title,
                torrent_hash='',  # Will be updated if needed
                quality=jackett_result.quality,
                indexer=jackett_result.indexer,
                size_bytes=jackett_result.size,
                seeders=jackett_result.seeders,
                real_debrid_id=torrent_id,
                real_debrid_links=links,
                status='monitoring',  # Start in monitoring since it's already downloaded
                source='rss',  # Mark as RSS download
                started_at=timezone.now(),
                cleanup_after_completion=cleanup_after_completion
            )

            logger.info(f"RSS auto-download created: {jackett_result.title} for {episode.season.tv_show.title} S{episode.season.season_number:02d}E{episode.episode_number:02d}")
            return True

        except RealDebridAPIError as e:
            logger.error(f"Real Debrid error downloading RSS torrent: {e}")
            return False
        except Exception as e:
            logger.error(f"Error downloading RSS torrent: {e}")
            return False


class RSSMonitoringService:
    """Main service for RSS monitoring operations"""

    def __init__(self):
        self.rss_service = JackettRSSService()

    def run_rss_monitoring(self, hours: int = 24) -> Dict[str, int]:
        """Run RSS monitoring cycle"""
        logger.info("Starting RSS monitoring cycle")

        try:
            # Check if Jackett is configured
            if not settings.JACKETT_API_KEY or not settings.JACKETT_BASE_URL:
                logger.warning("Jackett not configured, skipping RSS monitoring")
                return {'error': 'Jackett not configured'}

            # Check if Real Debrid is configured
            if not settings.REAL_DEBRID_API_KEY:
                logger.warning("Real Debrid not configured, skipping RSS monitoring")
                return {'error': 'Real Debrid not configured'}

            # Process RSS torrents
            stats = self.rss_service.process_rss_torrents(hours=hours)

            logger.info(f"RSS monitoring cycle completed: {stats}")
            return stats

        except Exception as e:
            logger.error(f"RSS monitoring cycle failed: {e}")
            return {'error': str(e)}
