"""
Scheduler for automatic Overseerr data synchronization
"""

import logging
from apscheduler.schedulers.background import BackgroundScheduler
from apscheduler.triggers.interval import IntervalTrigger
from django.conf import settings
from django.core.management import call_command
from django.utils import timezone

logger = logging.getLogger(__name__)

class OverseerrScheduler:
    """Scheduler for automatic Overseerr sync operations"""
    
    def __init__(self):
        self.scheduler = None
        self.is_running = False
    
    def start(self):
        """Start the scheduler"""
        if self.is_running:
            logger.warning("Scheduler is already running")
            return
        
        try:
            self.scheduler = BackgroundScheduler()
            
            # Add hourly sync job
            self.scheduler.add_job(
                func=self.sync_overseerr_data,
                trigger=IntervalTrigger(hours=1),
                id='overseerr_sync',
                name='Overseerr Data Sync',
                replace_existing=True,
                max_instances=1,  # Prevent overlapping jobs
                misfire_grace_time=300  # 5 minutes grace time
            )

            # Add download monitoring job (every minute)
            self.scheduler.add_job(
                func=self.monitor_downloads,
                trigger=IntervalTrigger(minutes=1),
                id='download_monitor',
                name='Download Status Monitor',
                replace_existing=True,
                max_instances=1,  # Prevent overlapping jobs
                misfire_grace_time=60  # 1 minute grace time
            )

            # Add RSS monitoring job (every 15 minutes)
            self.scheduler.add_job(
                func=self.monitor_rss,
                trigger=IntervalTrigger(minutes=15),
                id='rss_monitor',
                name='RSS Feed Monitor',
                replace_existing=True,
                max_instances=1,  # Prevent overlapping jobs
                misfire_grace_time=300  # 5 minutes grace time
            )
            
            self.scheduler.start()
            self.is_running = True
            logger.info("Scheduler started - Overseerr sync every hour, download monitoring every minute, RSS monitoring every 15 minutes")
            
        except Exception as e:
            logger.error(f"Failed to start scheduler: {e}")
            raise
    
    def stop(self):
        """Stop the scheduler"""
        if self.scheduler and self.is_running:
            try:
                self.scheduler.shutdown(wait=False)
                self.is_running = False
                logger.info("Overseerr scheduler stopped")
            except Exception as e:
                logger.error(f"Error stopping scheduler: {e}")
    
    def sync_overseerr_data(self):
        """Execute the Plex data sync"""
        try:
            logger.info("Starting scheduled Plex data sync")

            # Check if API is configured
            if not settings.OVERSEERR_API_KEY or not settings.OVERSEERR_BASE_URL:
                logger.warning("Overseerr API not configured, skipping sync")
                return

            # Call the management command (now syncs Plex data only)
            call_command('sync_overseerr')
            logger.info("Scheduled Plex data sync completed successfully")

        except Exception as e:
            logger.error(f"Scheduled Plex data sync failed: {e}")

    def monitor_downloads(self):
        """Execute download monitoring"""
        try:
            logger.info("Starting scheduled download monitoring")

            # Check if Real Debrid is configured
            if not settings.REAL_DEBRID_API_KEY:
                logger.debug("Real Debrid API not configured, skipping download monitoring")
                return

            # Call the management command
            call_command('monitor_downloads', '--once')
            logger.info("Scheduled download monitoring completed successfully")

        except Exception as e:
            logger.error(f"Scheduled download monitoring failed: {e}")

    def monitor_rss(self):
        """Execute RSS monitoring"""
        try:
            logger.info("Starting scheduled RSS monitoring")

            # Check if RSS monitoring is enabled
            from .models import AppSettings
            app_settings = AppSettings.get_settings()
            if not app_settings.enable_rss_monitoring:
                logger.debug("RSS monitoring disabled in settings, skipping")
                return

            # Check if Jackett and Real Debrid are configured
            if not settings.JACKETT_API_KEY or not settings.REAL_DEBRID_API_KEY:
                logger.debug("Jackett or Real Debrid API not configured, skipping RSS monitoring")
                return

            # Call the management command with configured hours
            hours = app_settings.rss_monitoring_hours
            call_command('monitor_rss', '--once', f'--hours={hours}')
            logger.info("Scheduled RSS monitoring completed successfully")

        except Exception as e:
            logger.error(f"Scheduled RSS monitoring failed: {e}")
    
    def get_status(self):
        """Get scheduler status"""
        if not self.scheduler:
            return {
                'running': False,
                'jobs': [],
                'next_run': None
            }
        
        jobs = []
        for job in self.scheduler.get_jobs():
            jobs.append({
                'id': job.id,
                'name': job.name,
                'next_run': job.next_run_time.isoformat() if job.next_run_time else None,
                'trigger': str(job.trigger)
            })
        
        return {
            'running': self.is_running,
            'jobs': jobs,
            'next_run': jobs[0]['next_run'] if jobs else None
        }

# Global scheduler instance
_scheduler = None

def get_scheduler():
    """Get the global scheduler instance"""
    global _scheduler
    if _scheduler is None:
        _scheduler = OverseerrScheduler()
    return _scheduler

def start_scheduler():
    """Start the global scheduler"""
    scheduler = get_scheduler()
    scheduler.start()

def stop_scheduler():
    """Stop the global scheduler"""
    scheduler = get_scheduler()
    scheduler.stop()

def get_scheduler_status():
    """Get the status of the global scheduler"""
    scheduler = get_scheduler()
    return scheduler.get_status()
