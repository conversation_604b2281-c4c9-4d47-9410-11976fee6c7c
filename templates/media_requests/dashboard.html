{% extends 'base.html' %}

{% block title %}Dashboard - Overseerr Dashboard{% endblock %}

{% block extra_css %}
<style>
    /* Toast styling */
    .toast {
        min-width: 300px;
        box-shadow: 0 4px 12px rgba(0,0,0,0.15);
    }

    .toast-header.bg-success-subtle {
        border-bottom: 1px solid rgba(25, 135, 84, 0.2);
    }

    .toast-header.bg-danger-subtle {
        border-bottom: 1px solid rgba(220, 53, 69, 0.2);
    }

    .toast-header.bg-warning-subtle {
        border-bottom: 1px solid rgba(255, 193, 7, 0.2);
    }

    /* Sortable table styling */
    .sortable {
        cursor: pointer;
        user-select: none;
        position: relative;
        transition: background-color 0.2s ease;
    }

    .sortable:hover {
        background-color: rgba(255, 255, 255, 0.1) !important;
    }

    .sort-icon {
        margin-left: 5px;
        font-size: 0.8em;
        transition: opacity 0.2s ease;
    }

    .sortable:not(:hover) .sort-icon.text-muted {
        opacity: 0.5;
    }

    .sortable:hover .sort-icon.text-muted {
        opacity: 0.8;
    }
</style>
{% endblock %}

{% block content %}
<!-- Statistics Cards -->
<div class="row mb-4">
    <div class="col-md-6 mb-3">
        <div class="card stats-card">
            <div class="card-body text-center">
                <h3 class="card-title">{{ stats.total_shows }}</h3>
                <p class="card-text">Total Shows</p>
            </div>
        </div>
    </div>
    <div class="col-md-6 mb-3">
        <div class="card bg-primary text-white">
            <div class="card-body text-center">
                <h3 class="card-title">{{ stats.shows_with_episodes }}</h3>
                <p class="card-text">Shows with Episodes</p>
            </div>
        </div>
    </div>
</div>

<!-- Filters and Search -->
<div class="card mb-4">
    <div class="card-body">
        <form method="get" class="row g-3">
            <div class="col-md-3">
                <label for="search" class="form-label">Search Shows</label>
                <input type="text" class="form-control" id="search" name="search"
                       value="{{ current_filters.search }}" placeholder="Search by title or description...">
            </div>

            <div class="col-md-2">
                <label for="sort" class="form-label">Sort By</label>
                <select class="form-select" id="sort" name="sort">
                    {% for value, label in sort_choices %}
                        <option value="{{ value }}" {% if current_filters.sort == value %}selected{% endif %}>
                            {{ label }}
                        </option>
                    {% endfor %}
                </select>
                <small class="text-muted">Or click table headers to sort</small>
            </div>
            <div class="col-md-2">
                <label for="view" class="form-label">View Mode</label>
                <select class="form-select" id="view" name="view">
                    <option value="grid" {% if current_filters.view == 'grid' %}selected{% endif %}>
                        <i class="bi bi-grid-3x3-gap"></i> Grid
                    </option>
                    <option value="table" {% if current_filters.view == 'table' %}selected{% endif %}>
                        <i class="bi bi-table"></i> Table
                    </option>
                </select>
            </div>
            <div class="col-md-2">
                <label class="form-label">&nbsp;</label>
                <div class="d-grid">
                    <button type="submit" class="btn btn-primary">
                        <i class="bi bi-funnel me-1"></i>Filter
                    </button>
                </div>
            </div>
            <div class="col-md-1">
                <label class="form-label">&nbsp;</label>
                <div class="d-grid">
                    <button type="button" class="btn btn-outline-secondary" onclick="addShow()">
                        <i class="bi bi-plus me-1"></i>Add
                    </button>
                </div>
            </div>
            <div class="col-md-1">
                <label class="form-label">&nbsp;</label>
                <div class="d-grid">
                    <button type="button" class="btn btn-outline-info" id="rssCheckBtn" onclick="triggerRSSCheck()"
                            title="Manually check RSS feeds for new torrents">
                        <i class="bi bi-rss me-1"></i>RSS
                    </button>
                </div>
            </div>
        </form>
    </div>
</div>

<!-- TV Shows Content -->
{% if page_obj %}
    {% if current_filters.view == 'table' %}
        <!-- Table View -->
        <div class="card">
            <div class="table-responsive">
                <table class="table table-hover mb-0">
                    <thead class="table-dark">
                        <tr>
                            <th style="width: 60px;"></th>
                            <th class="sortable" data-sort="title">
                                Title
                                {% if current_filters.sort == 'title' %}
                                    <i class="bi bi-arrow-up sort-icon"></i>
                                {% elif current_filters.sort == '-title' %}
                                    <i class="bi bi-arrow-down sort-icon"></i>
                                {% else %}
                                    <i class="bi bi-arrow-down-up sort-icon text-muted"></i>
                                {% endif %}
                            </th>
                            <th class="sortable" data-sort="status">
                                Status
                                {% if current_filters.sort == 'status' %}
                                    <i class="bi bi-arrow-up sort-icon"></i>
                                {% elif current_filters.sort == '-status' %}
                                    <i class="bi bi-arrow-down sort-icon"></i>
                                {% else %}
                                    <i class="bi bi-arrow-down-up sort-icon text-muted"></i>
                                {% endif %}
                            </th>
                            <th class="sortable" data-sort="seasons">
                                Seasons
                                {% if current_filters.sort == 'seasons' %}
                                    <i class="bi bi-arrow-up sort-icon"></i>
                                {% elif current_filters.sort == '-seasons' %}
                                    <i class="bi bi-arrow-down sort-icon"></i>
                                {% else %}
                                    <i class="bi bi-arrow-down-up sort-icon text-muted"></i>
                                {% endif %}
                            </th>
                            <th>Episodes</th>
                            <th class="sortable" data-sort="profile">
                                Profile
                                {% if current_filters.sort == 'profile' %}
                                    <i class="bi bi-arrow-up sort-icon"></i>
                                {% elif current_filters.sort == '-profile' %}
                                    <i class="bi bi-arrow-down sort-icon"></i>
                                {% else %}
                                    <i class="bi bi-arrow-down-up sort-icon text-muted"></i>
                                {% endif %}
                            </th>
                            <th class="sortable" data-sort="date_added">
                                Added Date
                                {% if current_filters.sort == 'date_added' %}
                                    <i class="bi bi-arrow-up sort-icon"></i>
                                {% elif current_filters.sort == '-date_added' %}
                                    <i class="bi bi-arrow-down sort-icon"></i>
                                {% else %}
                                    <i class="bi bi-arrow-down-up sort-icon text-muted"></i>
                                {% endif %}
                            </th>
                            <th>Actions</th>
                        </tr>
                    </thead>
                    <tbody>
                        {% for show in page_obj %}
                            <tr>
                                <td>
                                    <a href="{% url 'show_detail' show.id %}" class="text-decoration-none">
                                        {% if show.poster_path %}
                                            <img src="https://image.tmdb.org/t/p/w92{{ show.poster_path }}"
                                                 class="img-thumbnail" style="width: 50px; height: 75px; object-fit: cover;"
                                                 alt="{{ show.title }}">
                                        {% else %}
                                            <div class="bg-light d-flex align-items-center justify-content-center"
                                                 style="width: 50px; height: 75px;">
                                                <i class="bi bi-tv text-muted"></i>
                                            </div>
                                        {% endif %}
                                    </a>
                                </td>
                                <td>
                                    <div>
                                        <a href="{% url 'show_detail' show.id %}" class="text-decoration-none text-dark">
                                            <strong>{{ show.title }}</strong>
                                        </a>
                                        {% if show.first_air_date %}
                                            <small class="text-muted d-block">({{ show.first_air_date.year }})</small>
                                        {% endif %}
                                    </div>
                                </td>
                                <td>
                                    {% if show.status %}
                                        <span class="text-muted">{{ show.status }}</span>
                                    {% else %}
                                        <span class="text-muted">-</span>
                                    {% endif %}
                                </td>
                                <td>
                                    <span class="badge bg-secondary">
                                        {{ show.get_available_seasons }}/{{ show.get_total_seasons }}
                                    </span>
                                </td>
                                <td>
                                    <span class="badge bg-info">
                                        {{ show.get_available_episodes }}/{{ show.get_total_episodes }}
                                    </span>
                                </td>
                                <td>
                                    {% if show.quality_profile %}
                                        <span class="badge bg-primary">
                                            <i class="bi bi-sliders me-1"></i>{{ show.quality_profile.name }}
                                        </span>
                                        {% if show.quality_profile.is_default %}
                                            <small class="text-muted d-block">Default</small>
                                        {% endif %}
                                    {% else %}
                                        <span class="text-muted">No Profile</span>
                                    {% endif %}
                                </td>
                                <td>
                                    {% if show.added_at %}
                                        {{ show.added_at|date:"M d, Y" }}
                                    {% else %}
                                        <span class="text-muted">-</span>
                                    {% endif %}
                                </td>
                                <td>
                                    <div class="btn-group" role="group">
                                        <a href="{% url 'show_detail' show.id %}" class="btn btn-primary btn-sm">
                                            <i class="bi bi-eye"></i>
                                        </a>
                                        <button class="btn btn-danger btn-sm" onclick="deleteShow({{ show.id }}, '{{ show.title|escapejs }}')">
                                            <i class="bi bi-trash"></i>
                                        </button>
                                    </div>
                                </td>
                            </tr>
                        {% endfor %}
                    </tbody>
                </table>
            </div>
        </div>
    {% else %}
        <!-- Grid View -->
        <div class="row">
            {% for show in page_obj %}
                <div class="col-lg-3 col-md-4 col-sm-6 mb-4">
                    <div class="card card-hover h-100">
                        <div class="position-relative">
                            <a href="{% url 'show_detail' show.id %}" class="text-decoration-none">
                                {% if show.poster_path %}
                                    <img src="https://image.tmdb.org/t/p/w500{{ show.poster_path }}"
                                         class="poster-img" alt="{{ show.title }}">
                                {% else %}
                                    <div class="poster-img bg-light d-flex align-items-center justify-content-center">
                                        <i class="bi bi-tv text-muted" style="font-size: 3rem;"></i>
                                    </div>
                                {% endif %}
                            </a>


                        </div>

                        <div class="card-body">
                            <h5 class="card-title">
                                <a href="{% url 'show_detail' show.id %}" class="text-decoration-none text-dark">
                                    {{ show.title }}
                                </a>
                            </h5>
                            <p class="card-text text-muted small">
                                {% if show.first_air_date %}
                                    <i class="bi bi-calendar me-1"></i>{{ show.first_air_date.year }}
                                {% endif %}
                                {% if show.status %}
                                    <span class="ms-2">
                                        <i class="bi bi-info-circle me-1"></i>{{ show.status }}
                                    </span>
                                {% endif %}
                            </p>

                            <!-- Progress Info -->
                            <div class="mb-2">
                                <small class="text-muted">
                                    <i class="bi bi-collection me-1"></i>
                                    {{ show.get_available_seasons }}/{{ show.get_total_seasons }} seasons
                                    <span class="ms-2">
                                        <i class="bi bi-play me-1"></i>
                                        {{ show.get_available_episodes }}/{{ show.get_total_episodes }} episodes
                                    </span>
                                </small>
                            </div>

                            <!-- Quality Profile Info -->
                            <div class="mb-2">
                                {% if show.quality_profile %}
                                    <span class="badge bg-primary">
                                        <i class="bi bi-sliders me-1"></i>{{ show.quality_profile.name }}
                                    </span>
                                    {% if show.quality_profile.is_default %}
                                        <small class="text-muted ms-1">Default</small>
                                    {% endif %}
                                {% else %}
                                    <span class="badge bg-secondary">No Profile</span>
                                {% endif %}
                            </div>

                            <!-- Added Info -->
                            {% if show.added_by or show.added_at %}
                                <div class="mb-2">
                                    <small class="text-muted">
                                        {% if show.added_by %}
                                            <i class="bi bi-person me-1"></i>{{ show.added_by }}
                                        {% endif %}
                                        {% if show.added_at %}
                                            <span class="ms-2">
                                                <i class="bi bi-clock me-1"></i>{{ show.added_at|date:"M d, Y" }}
                                            </span>
                                        {% endif %}
                                    </small>
                                </div>
                            {% endif %}

                            <p class="card-text">
                                {{ show.overview|truncatechars:100 }}
                            </p>
                        </div>

                        <div class="card-footer bg-transparent">
                            <div class="d-flex justify-content-between">
                                <a href="{% url 'show_detail' show.id %}" class="btn btn-primary btn-sm">
                                    <i class="bi bi-eye me-1"></i>View Details
                                </a>
                                <button class="btn btn-danger btn-sm" onclick="deleteShow({{ show.id }}, '{{ show.title|escapejs }}')">
                                    <i class="bi bi-trash"></i>
                                </button>
                            </div>
                        </div>
                    </div>
                </div>
            {% endfor %}
        </div>
    {% endif %}

    <!-- Pagination -->
    {% if page_obj.has_other_pages %}
        <nav aria-label="Page navigation" class="mt-4">
            <ul class="pagination justify-content-center">
                {% if page_obj.has_previous %}
                    <li class="page-item">
                        <a class="page-link" href="?{% if current_filters.search %}search={{ current_filters.search }}&{% endif %}{% if current_filters.sort %}sort={{ current_filters.sort }}&{% endif %}page={{ page_obj.previous_page_number }}">
                            <i class="bi bi-chevron-left"></i>
                        </a>
                    </li>
                {% endif %}

                {% for num in page_obj.paginator.page_range %}
                    {% if page_obj.number == num %}
                        <li class="page-item active">
                            <span class="page-link">{{ num }}</span>
                        </li>
                    {% elif num > page_obj.number|add:'-3' and num < page_obj.number|add:'3' %}
                        <li class="page-item">
                            <a class="page-link" href="?{% if current_filters.search %}search={{ current_filters.search }}&{% endif %}{% if current_filters.sort %}sort={{ current_filters.sort }}&{% endif %}page={{ num }}">{{ num }}</a>
                        </li>
                    {% endif %}
                {% endfor %}

                {% if page_obj.has_next %}
                    <li class="page-item">
                        <a class="page-link" href="?{% if current_filters.search %}search={{ current_filters.search }}&{% endif %}{% if current_filters.sort %}sort={{ current_filters.sort }}&{% endif %}page={{ page_obj.next_page_number }}">
                            <i class="bi bi-chevron-right"></i>
                        </a>
                    </li>
                {% endif %}
            </ul>
        </nav>
    {% endif %}
{% else %}
    <!-- Empty State -->
    <div class="text-center py-5">
        <i class="bi bi-tv text-muted" style="font-size: 4rem;"></i>
        <h3 class="mt-3 text-muted">No TV Shows Found</h3>
        <p class="text-muted">
            {% if current_filters.search %}
                Try adjusting your search terms.
            {% else %}
                Start by syncing data from Overseerr using the "Sync Data" button above.
            {% endif %}
        </p>
        {% if not current_filters.search and not current_filters.availability %}
            <button class="btn btn-primary" onclick="syncData()">
                <i class="bi bi-arrow-clockwise me-1"></i>Sync Data from Overseerr
            </button>
        {% endif %}
    </div>
{% endif %}

<!-- Search Modal -->
<div class="modal fade" id="searchModal" tabindex="-1" aria-labelledby="searchModalLabel" aria-hidden="true">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="searchModalLabel">
                    <i class="bi bi-search me-2"></i>Search and Add TV Shows
                </h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body">
                <div class="mb-3">
                    <input type="text" class="form-control" id="searchQuery"
                           placeholder="Search for TV shows..."
                           oninput="searchShows()">
                </div>
                <div id="searchResults" style="max-height: 400px; overflow-y: auto;">
                    <div class="text-muted text-center py-3">
                        <i class="bi bi-search" style="font-size: 2rem;"></i>
                        <p class="mt-2">Start typing to search for TV shows</p>
                    </div>
                </div>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Close</button>
            </div>
        </div>
    </div>
</div>

<!-- Toast Container -->
<div class="toast-container position-fixed top-0 end-0 p-3" style="z-index: 1055;">
    <div id="notificationToast" class="toast" role="alert" aria-live="assertive" aria-atomic="true">
        <div class="toast-header">
            <i id="toastIcon" class="bi bi-check-circle-fill text-success me-2"></i>
            <strong id="toastTitle" class="me-auto">Success</strong>
            <button type="button" class="btn-close" data-bs-dismiss="toast" aria-label="Close"></button>
        </div>
        <div class="toast-body" id="toastMessage">
            Operation completed successfully.
        </div>
    </div>
</div>

{% csrf_token %}
{% endblock %}

{% block extra_js %}
<script>
    // Auto-submit form on filter change and handle sortable columns
    document.addEventListener('DOMContentLoaded', function() {
        const sortSelect = document.getElementById('sort');
        const viewSelect = document.getElementById('view');

        sortSelect.addEventListener('change', function() {
            this.form.submit();
        });

        viewSelect.addEventListener('change', function() {
            this.form.submit();
        });

        // Handle sortable column clicks
        document.querySelectorAll('.sortable').forEach(function(header) {
            header.addEventListener('click', function() {
                const sortField = this.dataset.sort;
                const currentSort = new URLSearchParams(window.location.search).get('sort');

                let newSort;
                if (currentSort === sortField) {
                    // If currently sorting by this field ascending, switch to descending
                    newSort = '-' + sortField;
                } else if (currentSort === '-' + sortField) {
                    // If currently sorting by this field descending, switch to ascending
                    newSort = sortField;
                } else {
                    // If not currently sorting by this field, start with ascending
                    newSort = sortField;
                }

                // Update the sort parameter and reload
                const url = new URL(window.location);
                url.searchParams.set('sort', newSort);
                url.searchParams.delete('page'); // Reset to first page when sorting
                window.location.href = url.toString();
            });
        });
    });

    // Toast notification functions
    function showToast(title, message, type = 'success') {
        const toast = document.getElementById('notificationToast');
        const toastTitle = document.getElementById('toastTitle');
        const toastMessage = document.getElementById('toastMessage');
        const toastIcon = document.getElementById('toastIcon');

        // Set content
        toastTitle.textContent = title;
        toastMessage.textContent = message;

        // Set icon and colors based on type
        if (type === 'success') {
            toastIcon.className = 'bi bi-check-circle-fill text-success me-2';
            toast.querySelector('.toast-header').className = 'toast-header bg-success-subtle';
        } else if (type === 'error') {
            toastIcon.className = 'bi bi-exclamation-triangle-fill text-danger me-2';
            toast.querySelector('.toast-header').className = 'toast-header bg-danger-subtle';
        } else if (type === 'warning') {
            toastIcon.className = 'bi bi-exclamation-triangle-fill text-warning me-2';
            toast.querySelector('.toast-header').className = 'toast-header bg-warning-subtle';
        }

        // Show the toast
        const bsToast = new bootstrap.Toast(toast, {
            autohide: true,
            delay: 5000
        });
        bsToast.show();
    }

    // Add show function
    function addShow() {
        // Show the search modal
        const modal = new bootstrap.Modal(document.getElementById('searchModal'));
        modal.show();
    }

    // Search functionality
    let searchTimeout;
    function searchShows() {
        const query = document.getElementById('searchQuery').value.trim();
        if (query.length < 2) {
            document.getElementById('searchResults').innerHTML = '';
            return;
        }

        // Debounce search
        clearTimeout(searchTimeout);
        searchTimeout = setTimeout(() => {
            fetch(`/api/search/?q=${encodeURIComponent(query)}`)
                .then(response => response.json())
                .then(data => {
                    displaySearchResults(data.results || []);
                })
                .catch(error => {
                    console.error('Search error:', error);
                    showToast('Search Error', 'Search failed. Please try again.', 'error');
                    document.getElementById('searchResults').innerHTML =
                        '<div class="alert alert-danger">Search failed. Please try again.</div>';
                });
        }, 300);
    }

    function displaySearchResults(results) {
        const container = document.getElementById('searchResults');

        if (results.length === 0) {
            container.innerHTML = '<div class="text-muted text-center py-3">No results found</div>';
            return;
        }

        const html = results.map(show => `
            <div class="card mb-2">
                <div class="card-body">
                    <div class="row align-items-center">
                        <div class="col-2">
                            ${show.posterPath ?
                                `<img src="https://image.tmdb.org/t/p/w92${show.posterPath}" class="img-thumbnail" style="width: 60px;">` :
                                '<div class="bg-light d-flex align-items-center justify-content-center" style="width: 60px; height: 90px;"><i class="bi bi-tv"></i></div>'
                            }
                        </div>
                        <div class="col-8">
                            <h6 class="mb-1">${show.name}</h6>
                            <small class="text-muted">
                                ${show.firstAirDate ? new Date(show.firstAirDate).getFullYear() : 'Unknown'}
                                ${show.status ? ` • ${show.status}` : ''}
                            </small>
                            <p class="mb-0 small">${(show.overview || '').substring(0, 150)}${show.overview && show.overview.length > 150 ? '...' : ''}</p>
                        </div>
                        <div class="col-2">
                            <button class="btn btn-primary btn-sm" onclick="requestShow(${show.id}, '${show.name.replace(/'/g, "\\'")}')">
                                <i class="bi bi-plus"></i> Add
                            </button>
                        </div>
                    </div>
                </div>
            </div>
        `).join('');

        container.innerHTML = html;
    }

    function requestShow(tmdbId, showName) {
        if (!confirm(`Add "${showName}" to your library?`)) {
            return;
        }

        fetch('/api/add/', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
                'X-CSRFToken': document.querySelector('[name=csrfmiddlewaretoken]').value
            },
            body: JSON.stringify({
                tmdb_id: tmdbId,
                added_by: 'Web User'
            })
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                showToast('Show Added', data.message, 'success');
                // Close modal and refresh page after a short delay
                bootstrap.Modal.getInstance(document.getElementById('searchModal')).hide();
                setTimeout(() => {
                    window.location.reload();
                }, 1500);
            } else {
                showToast('Error', data.error || 'Unknown error', 'error');
            }
        })
        .catch(error => {
            console.error('Add show error:', error);
            showToast('Error', 'Failed to add show. Please try again.', 'error');
        });
    }

    // Delete show function
    function deleteShow(showId, showTitle) {
        if (!confirm(`Are you sure you want to delete "${showTitle}"?\n\nThis will remove the show and all its episodes from your library.`)) {
            return;
        }

        fetch(`/api/delete/${showId}/`, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
                'X-CSRFToken': document.querySelector('[name=csrfmiddlewaretoken]').value
            },
            body: JSON.stringify({
                deleted_by: 'Web User'
            })
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                showToast('Show Deleted', data.message, 'success');
                // Refresh the page after a short delay
                setTimeout(() => {
                    window.location.reload();
                }, 1500);
            } else {
                showToast('Error', data.error || 'Unknown error', 'error');
            }
        })
        .catch(error => {
            console.error('Delete show error:', error);
            showToast('Error', 'Failed to delete show. Please try again.', 'error');
        });
    }

    // RSS trigger functionality
    async function triggerRSSCheck() {
        const button = document.getElementById('rssCheckBtn');
        const originalContent = button.innerHTML;

        try {
            // Update button to show loading state
            button.innerHTML = '<i class="bi bi-arrow-clockwise spin me-1"></i>Checking...';
            button.disabled = true;

            // Get CSRF token
            const csrfToken = document.querySelector('[name=csrfmiddlewaretoken]').value;

            // Make API call
            const response = await fetch('/api/rss/trigger/', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                    'X-CSRFToken': csrfToken
                },
                body: JSON.stringify({
                    hours: 24  // Check last 24 hours
                })
            });

            const data = await response.json();

            if (data.success) {
                const stats = data.stats;
                const message = `RSS check completed! Found ${stats.total_items} items, matched ${stats.matched_episodes} episodes, started ${stats.downloads_started} downloads.`;
                showToast('RSS Check Complete', message, 'success');

                // Refresh the page after a short delay to show any new downloads
                setTimeout(() => {
                    window.location.reload();
                }, 2000);
            } else {
                showToast('RSS Check Failed', data.error || 'Unknown error occurred', 'error');
            }

        } catch (error) {
            console.error('RSS check error:', error);
            showToast('RSS Check Error', 'Failed to trigger RSS check. Please try again.', 'error');
        } finally {
            // Restore button state
            button.innerHTML = originalContent;
            button.disabled = false;
        }
    }

    // Add CSS for spinning animation
    const style = document.createElement('style');
    style.textContent = `
        .spin {
            animation: spin 1s linear infinite;
        }
        @keyframes spin {
            from { transform: rotate(0deg); }
            to { transform: rotate(360deg); }
        }
    `;
    document.head.appendChild(style);
</script>
{% endblock %}
