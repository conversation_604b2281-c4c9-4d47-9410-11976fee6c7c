{% extends 'base.html' %}

{% block title %}Downloads Monitor{% endblock %}

{% block content %}
<div class="container-fluid">
    <div class="row">
        <div class="col-12">
            <div class="d-flex justify-content-between align-items-center mb-4">
                <h1><i class="bi bi-cloud-download me-2"></i>Downloads Monitor</h1>
                <div>
                    <button class="btn btn-outline-primary me-2" onclick="refreshDownloads()">
                        <i class="bi bi-arrow-clockwise"></i> Refresh
                    </button>
                    <a href="{% url 'dashboard' %}" class="btn btn-secondary">
                        <i class="bi bi-arrow-left"></i> Back to Dashboard
                    </a>
                </div>
            </div>

            <!-- Auto-refresh toggle -->
            <div class="card mb-4">
                <div class="card-body">
                    <div class="d-flex align-items-center">
                        <div class="form-check me-3">
                            <input class="form-check-input" type="checkbox" id="autoRefresh" checked>
                            <label class="form-check-label" for="autoRefresh">
                                Auto-refresh every 30 seconds
                            </label>
                        </div>
                        <div class="text-muted">
                            <small>Last updated: <span id="lastUpdated">Never</span></small>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Downloads table -->
            <div class="card">
                <div class="card-header">
                    <h5 class="card-title mb-0">
                        <i class="bi bi-list-ul me-2"></i>Active Downloads
                        <span class="badge bg-primary ms-2" id="downloadCount">0</span>
                    </h5>
                </div>
                <div class="card-body">
                    <!-- Loading state -->
                    <div id="loadingState" class="text-center py-4">
                        <div class="spinner-border text-primary" role="status">
                            <span class="visually-hidden">Loading...</span>
                        </div>
                        <p class="mt-2 text-muted">Loading downloads...</p>
                    </div>

                    <!-- Error state -->
                    <div id="errorState" class="alert alert-danger d-none" role="alert">
                        <i class="bi bi-exclamation-triangle me-2"></i>
                        <span id="errorMessage">Failed to load downloads</span>
                    </div>

                    <!-- Empty state -->
                    <div id="emptyState" class="text-center py-4 d-none">
                        <i class="bi bi-inbox display-1 text-muted"></i>
                        <h4 class="mt-3 text-muted">No Active Downloads</h4>
                        <p class="text-muted">All downloads are completed or there are no active downloads.</p>
                    </div>

                    <!-- Downloads table -->
                    <div id="downloadsTable" class="d-none">
                        <div class="table-responsive">
                            <table class="table table-hover">
                                <thead>
                                    <tr>
                                        <th>Show</th>
                                        <th>Episode</th>
                                        <th>Torrent</th>
                                        <th>Status</th>
                                        <th>Progress</th>
                                        <th>Quality</th>
                                        <th>Size</th>
                                        <th>Started</th>
                                        <th>Actions</th>
                                    </tr>
                                </thead>
                                <tbody id="downloadsTableBody">
                                    <!-- Downloads will be populated here -->
                                </tbody>
                            </table>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<script>
let autoRefreshInterval = null;
let lastRefreshTime = null;

document.addEventListener('DOMContentLoaded', function() {
    // Initial load
    refreshDownloads();
    
    // Setup auto-refresh
    setupAutoRefresh();
    
    // Auto-refresh checkbox handler
    document.getElementById('autoRefresh').addEventListener('change', function() {
        setupAutoRefresh();
    });
});

function setupAutoRefresh() {
    // Clear existing interval
    if (autoRefreshInterval) {
        clearInterval(autoRefreshInterval);
        autoRefreshInterval = null;
    }
    
    // Setup new interval if enabled
    if (document.getElementById('autoRefresh').checked) {
        autoRefreshInterval = setInterval(refreshDownloads, 30000); // 30 seconds
    }
}

async function refreshDownloads() {
    try {
        showLoadingState();
        
        const response = await fetch('{% url "downloads_status_api" %}', {
            method: 'GET',
            headers: {
                'X-CSRFToken': getCookie('csrftoken'),
                'Content-Type': 'application/json',
            },
        });
        
        if (!response.ok) {
            throw new Error(`HTTP ${response.status}: ${response.statusText}`);
        }
        
        const data = await response.json();
        
        if (data.success) {
            displayDownloads(data.downloads);
            updateLastRefreshTime();
        } else {
            throw new Error(data.error || 'Unknown error');
        }
        
    } catch (error) {
        console.error('Failed to refresh downloads:', error);
        showErrorState(error.message);
    }
}

function showLoadingState() {
    document.getElementById('loadingState').classList.remove('d-none');
    document.getElementById('errorState').classList.add('d-none');
    document.getElementById('emptyState').classList.add('d-none');
    document.getElementById('downloadsTable').classList.add('d-none');
}

function showErrorState(message) {
    document.getElementById('loadingState').classList.add('d-none');
    document.getElementById('errorState').classList.remove('d-none');
    document.getElementById('emptyState').classList.add('d-none');
    document.getElementById('downloadsTable').classList.add('d-none');
    document.getElementById('errorMessage').textContent = message;
}

function showEmptyState() {
    document.getElementById('loadingState').classList.add('d-none');
    document.getElementById('errorState').classList.add('d-none');
    document.getElementById('emptyState').classList.remove('d-none');
    document.getElementById('downloadsTable').classList.add('d-none');
    document.getElementById('downloadCount').textContent = '0';
}

function displayDownloads(downloads) {
    document.getElementById('loadingState').classList.add('d-none');
    document.getElementById('errorState').classList.add('d-none');
    
    if (downloads.length === 0) {
        showEmptyState();
        return;
    }
    
    document.getElementById('emptyState').classList.add('d-none');
    document.getElementById('downloadsTable').classList.remove('d-none');
    document.getElementById('downloadCount').textContent = downloads.length;
    
    const tbody = document.getElementById('downloadsTableBody');
    tbody.innerHTML = '';
    
    downloads.forEach(download => {
        const row = createDownloadRow(download);
        tbody.appendChild(row);
    });
}

function createDownloadRow(download) {
    const row = document.createElement('tr');
    
    // Status badge
    const statusBadge = getStatusBadge(download.status);
    
    // Progress bar
    const progressBar = createProgressBar(download);
    
    // Format started time
    const startedTime = download.started_at ? 
        new Date(download.started_at).toLocaleString() : 'Unknown';
    
    row.innerHTML = `
        <td>
            <strong>${escapeHtml(download.show_title)}</strong>
        </td>
        <td>
            S${download.season_number.toString().padStart(2, '0')}E${download.episode_number.toString().padStart(2, '0')}
            ${download.episode_name ? '<br><small class="text-muted">' + escapeHtml(download.episode_name) + '</small>' : ''}
        </td>
        <td>
            <div class="text-truncate" style="max-width: 200px;" title="${escapeHtml(download.torrent_title)}">
                ${escapeHtml(download.torrent_title)}
            </div>
            ${download.indexer ? '<small class="text-muted">' + escapeHtml(download.indexer) + '</small>' : ''}
        </td>
        <td>
            ${statusBadge}
            ${download.real_debrid_status ? '<br><small class="text-muted">RD: ' + escapeHtml(download.real_debrid_status) + '</small>' : ''}
        </td>
        <td>
            ${progressBar}
        </td>
        <td>
            <span class="badge bg-info">${escapeHtml(download.quality || 'Unknown')}</span>
        </td>
        <td>
            <small>${escapeHtml(download.size_formatted || 'Unknown')}</small>
        </td>
        <td>
            <small>${startedTime}</small>
        </td>
        <td>
            <button class="btn btn-sm btn-outline-danger" 
                    onclick="cancelDownload(${download.id})"
                    title="Cancel download">
                <i class="bi bi-x-circle"></i>
            </button>
        </td>
    `;
    
    return row;
}

function getStatusBadge(status) {
    const badges = {
        'searching': '<span class="badge bg-secondary">Searching</span>',
        'pending': '<span class="badge bg-warning">Pending</span>',
        'caching': '<span class="badge bg-info">Caching</span>',
        'downloading': '<span class="badge bg-primary">Downloading</span>',
        'monitoring': '<span class="badge bg-success">Monitoring</span>',
        'completed': '<span class="badge bg-success">Completed</span>',
        'failed': '<span class="badge bg-danger">Failed</span>',
    };
    
    return badges[status] || '<span class="badge bg-secondary">Unknown</span>';
}

function createProgressBar(download) {
    let progress = 0;
    let progressText = '';
    
    if (download.status === 'caching') {
        progress = download.real_debrid_progress || 0;
        progressText = `Caching: ${progress}%`;
    } else if (download.status === 'downloading') {
        progress = download.real_debrid_progress || 0;
        progressText = `Downloading: ${progress}%`;
    } else if (download.status === 'completed') {
        progress = 100;
        progressText = 'Complete';
    } else {
        progressText = download.status.charAt(0).toUpperCase() + download.status.slice(1);
    }
    
    return `
        <div class="progress" style="height: 20px;">
            <div class="progress-bar" role="progressbar" 
                 style="width: ${progress}%" 
                 aria-valuenow="${progress}" 
                 aria-valuemin="0" 
                 aria-valuemax="100">
                <small>${progressText}</small>
            </div>
        </div>
    `;
}

function updateLastRefreshTime() {
    lastRefreshTime = new Date();
    document.getElementById('lastUpdated').textContent = lastRefreshTime.toLocaleTimeString();
}

async function cancelDownload(downloadId) {
    if (!confirm('Are you sure you want to cancel this download?')) {
        return;
    }
    
    try {
        const response = await fetch(`/api/episode/${downloadId}/remove/`, {
            method: 'POST',
            headers: {
                'X-CSRFToken': getCookie('csrftoken'),
                'Content-Type': 'application/json',
            },
        });
        
        if (response.ok) {
            // Refresh the downloads list
            refreshDownloads();
        } else {
            throw new Error('Failed to cancel download');
        }
        
    } catch (error) {
        console.error('Failed to cancel download:', error);
        alert('Failed to cancel download. Please try again.');
    }
}

function escapeHtml(text) {
    const div = document.createElement('div');
    div.textContent = text;
    return div.innerHTML;
}

// Utility function to get CSRF token
function getCookie(name) {
    let cookieValue = null;
    if (document.cookie && document.cookie !== '') {
        const cookies = document.cookie.split(';');
        for (let i = 0; i < cookies.length; i++) {
            const cookie = cookies[i].trim();
            if (cookie.substring(0, name.length + 1) === (name + '=')) {
                cookieValue = decodeURIComponent(cookie.substring(name.length + 1));
                break;
            }
        }
    }
    return cookieValue;
}
</script>
{% endblock %}
