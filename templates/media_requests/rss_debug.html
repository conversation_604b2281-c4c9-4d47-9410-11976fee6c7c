{% extends 'base.html' %}

{% block title %}RSS Debug - Overseerr Dashboard{% endblock %}

{% block extra_css %}
<style>
    .rss-item {
        border: 1px solid #dee2e6;
        border-radius: 0.375rem;
        margin-bottom: 1rem;
        transition: all 0.2s ease;
    }

    .rss-item:hover {
        box-shadow: 0 2px 8px rgba(0,0,0,0.1);
    }

    .status-badge {
        font-size: 0.75em;
        padding: 0.25rem 0.5rem;
    }

    .status-no-parse { background-color: #6c757d; }
    .status-parsed { background-color: #17a2b8; }
    .status-matched { background-color: #28a745; }
    .status-no-match { background-color: #dc3545; }

    .download-yes { background-color: #28a745; }
    .download-no { background-color: #dc3545; }

    .quality-score {
        font-weight: bold;
        padding: 0.25rem 0.5rem;
        border-radius: 0.25rem;
        font-size: 0.875em;
    }

    .score-high { background-color: #d4edda; color: #155724; }
    .score-medium { background-color: #fff3cd; color: #856404; }
    .score-low { background-color: #f8d7da; color: #721c24; }

    .rss-stats {
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        color: white;
        border-radius: 0.5rem;
        padding: 1.5rem;
        margin-bottom: 2rem;
    }

    .stat-item {
        text-align: center;
    }

    .stat-number {
        font-size: 2rem;
        font-weight: bold;
        display: block;
    }

    .stat-label {
        font-size: 0.875rem;
        opacity: 0.9;
    }

    .torrent-title {
        font-family: 'Courier New', monospace;
        font-size: 0.875em;
        background-color: #f8f9fa;
        padding: 0.5rem;
        border-radius: 0.25rem;
        word-break: break-all;
    }

    .parsed-info {
        background-color: #e3f2fd;
        padding: 0.75rem;
        border-radius: 0.25rem;
        margin: 0.5rem 0;
    }

    .match-info {
        background-color: #f3e5f5;
        padding: 0.75rem;
        border-radius: 0.25rem;
        margin: 0.5rem 0;
    }

    .download-info {
        background-color: #fff8e1;
        padding: 0.75rem;
        border-radius: 0.25rem;
        margin: 0.5rem 0;
    }

    .show-matches {
        max-height: 150px;
        overflow-y: auto;
    }

    .filter-controls {
        background-color: #f8f9fa;
        padding: 1rem;
        border-radius: 0.375rem;
        margin-bottom: 1rem;
    }
</style>
{% endblock %}

{% block content %}
<div class="d-flex justify-content-between align-items-center mb-4">
    <h1><i class="bi bi-rss me-2"></i>RSS Debug</h1>
    <div>
        <a href="{% url 'dashboard' %}" class="btn btn-outline-primary">
            <i class="bi bi-arrow-left me-1"></i>Back to Dashboard
        </a>
    </div>
</div>

{% if error %}
    <div class="alert alert-danger">
        <i class="bi bi-exclamation-triangle me-2"></i>{{ error }}
    </div>
{% else %}
    <!-- Statistics -->
    <div class="rss-stats">
        <div class="row">
            <div class="col-md-2 stat-item">
                <span class="stat-number">{{ stats.total_items }}</span>
                <span class="stat-label">Total Items</span>
            </div>
            <div class="col-md-2 stat-item">
                <span class="stat-number">{{ stats.parsed_items }}</span>
                <span class="stat-label">Parsed</span>
            </div>
            <div class="col-md-2 stat-item">
                <span class="stat-number">{{ stats.matched_episodes }}</span>
                <span class="stat-label">Matched</span>
            </div>
            <div class="col-md-2 stat-item">
                <span class="stat-number">{{ stats.downloadable }}</span>
                <span class="stat-label">Downloadable</span>
            </div>
            <div class="col-md-2 stat-item">
                <span class="stat-number">{{ stats.skipped }}</span>
                <span class="stat-label">Skipped</span>
            </div>
            <div class="col-md-2 stat-item">
                <span class="stat-number">{{ hours }}h</span>
                <span class="stat-label">Time Range</span>
            </div>
        </div>
    </div>

    <!-- Filter Controls -->
    <div class="filter-controls">
        <form method="get" class="row g-3 align-items-end">
            <div class="col-md-3">
                <label for="hours" class="form-label">Hours to check</label>
                <select class="form-select" id="hours" name="hours">
                    <option value="1" {% if hours == 1 %}selected{% endif %}>Last 1 hour</option>
                    <option value="6" {% if hours == 6 %}selected{% endif %}>Last 6 hours</option>
                    <option value="12" {% if hours == 12 %}selected{% endif %}>Last 12 hours</option>
                    <option value="24" {% if hours == 24 %}selected{% endif %}>Last 24 hours</option>
                    <option value="48" {% if hours == 48 %}selected{% endif %}>Last 48 hours</option>
                    <option value="72" {% if hours == 72 %}selected{% endif %}>Last 72 hours</option>
                    <option value="168" {% if hours == 168 %}selected{% endif %}>Last week</option>
                </select>
            </div>
            <div class="col-md-2">
                <button type="submit" class="btn btn-primary">
                    <i class="bi bi-funnel me-1"></i>Update
                </button>
            </div>
            <div class="col-md-2">
                <button type="button" class="btn btn-outline-info" onclick="triggerRSSCheck()">
                    <i class="bi bi-arrow-clockwise me-1"></i>Refresh RSS
                </button>
            </div>
        </form>
    </div>

    <!-- RSS Items -->
    {% if items %}
        <div class="row">
            {% for item in items %}
                <div class="col-12">
                    <div class="rss-item">
                        <div class="card-header d-flex justify-content-between align-items-center">
                            <div class="d-flex align-items-center gap-2">
                                <span class="badge status-badge status-{{ item.matching.status }}">
                                    {% if item.matching.status == 'no_parse' %}
                                        Not Parsed
                                    {% elif item.matching.status == 'parsed' %}
                                        Parsed
                                    {% elif item.matching.status == 'matched' %}
                                        Matched
                                    {% elif item.matching.status == 'no_match' %}
                                        No Match
                                    {% endif %}
                                </span>

                                {% if item.download_analysis.should_download %}
                                    <span class="badge download-yes">Will Download</span>
                                {% else %}
                                    <span class="badge download-no">Skip</span>
                                {% endif %}

                                <small class="text-muted">
                                    {{ item.rss_item.indexer }} •
                                    {{ item.rss_item.size|filesizeformat }} •
                                    {{ item.rss_item.seeders }} seeders
                                </small>
                            </div>

                            <small class="text-muted">
                                {{ item.rss_item.pub_date|date:"M d, H:i" }}
                            </small>
                        </div>

                        <div class="card-body">
                            <!-- Torrent Title -->
                            <div class="torrent-title mb-3">
                                {{ item.rss_item.title }}
                            </div>

                            <div class="row">
                                <!-- Parsed Information -->
                                <div class="col-md-4">
                                    <div class="parsed-info">
                                        <h6><i class="bi bi-code me-1"></i>Parsed Info</h6>
                                        {% if item.parsed_info.show_title %}
                                            <p class="mb-1"><strong>Show:</strong> {{ item.parsed_info.show_title }}</p>
                                            <p class="mb-1"><strong>Season:</strong> {{ item.parsed_info.season|default:"?" }}</p>
                                            <p class="mb-1"><strong>Episode:</strong> {{ item.parsed_info.episode|default:"?" }}</p>
                                            <p class="mb-1"><strong>Quality:</strong> {{ item.parsed_info.quality }}</p>
                                            <p class="mb-1"><small class="text-muted">Pattern: S{{ item.parsed_info.season|default:"?" }}E{{ item.parsed_info.episode|default:"?" }}</small></p>

                                            <!-- URL Validation -->
                                            <div class="mt-2">
                                                <strong>URLs:</strong><br>
                                                {% if item.url_validation.magnet_valid %}
                                                    <span class="badge bg-success">✓ Magnet</span>
                                                {% elif item.url_validation.has_magnet %}
                                                    <span class="badge bg-warning">⚠ Invalid Magnet</span>
                                                {% else %}
                                                    <span class="badge bg-secondary">✗ No Magnet</span>
                                                {% endif %}

                                                {% if item.url_validation.download_url_valid %}
                                                    <span class="badge bg-success">✓ Download URL</span>
                                                {% elif item.url_validation.has_download_url %}
                                                    <span class="badge bg-warning">⚠ Invalid Download URL</span>
                                                {% else %}
                                                    <span class="badge bg-secondary">✗ No Download URL</span>
                                                {% endif %}

                                                {% if not item.url_validation.has_valid_url %}
                                                    <br><small class="text-danger">⚠ No valid URLs found - cannot download</small>
                                                {% endif %}
                                            </div>
                                        {% else %}
                                            <p class="text-muted mb-1">Could not parse episode information</p>
                                            <p class="mb-0"><small class="text-muted">No S##E## or ##x## pattern found</small></p>
                                        {% endif %}
                                    </div>
                                </div>

                                <!-- Matching Information -->
                                <div class="col-md-4">
                                    <div class="match-info">
                                        <h6><i class="bi bi-search me-1"></i>Matching</h6>
                                        {% if item.matching.episode %}
                                            <p class="mb-1"><strong>Found:</strong> {{ item.matching.episode.season.tv_show.title }}</p>
                                            <p class="mb-1"><strong>Season:</strong> {{ item.matching.episode.season.season_number }}</p>
                                            <p class="mb-0"><strong>Episode:</strong> {{ item.matching.episode.episode_number }} - {{ item.matching.episode.name|default:"No title" }}</p>
                                        {% elif item.matching.show_matches %}
                                            <p class="mb-1"><strong>Potential shows:</strong></p>
                                            <div class="show-matches">
                                                {% for show in item.matching.show_matches %}
                                                    <small class="d-block">• {{ show.title }}</small>
                                                {% endfor %}
                                            </div>
                                        {% else %}
                                            <p class="text-muted mb-0">No matching shows found</p>
                                        {% endif %}
                                    </div>
                                </div>

                                <!-- Download Analysis -->
                                <div class="col-md-4">
                                    <div class="download-info">
                                        <h6><i class="bi bi-download me-1"></i>Download Analysis</h6>
                                        <p class="mb-1"><strong>Decision:</strong>
                                            {% if item.download_analysis.should_download %}
                                                <span class="text-success">Download</span>
                                            {% else %}
                                                <span class="text-danger">Skip</span>
                                            {% endif %}
                                        </p>
                                        <p class="mb-1"><strong>Reason:</strong> {{ item.download_analysis.reason }}</p>
                                        {% if item.download_analysis.quality_score %}
                                            <p class="mb-1"><strong>Score:</strong>
                                                <span class="quality-score {% if item.download_analysis.quality_score >= 1000 %}score-high{% elif item.download_analysis.quality_score >= 0 %}score-medium{% else %}score-low{% endif %}">
                                                    {{ item.download_analysis.quality_score }}
                                                </span>
                                            </p>
                                        {% endif %}
                                        {% if item.download_analysis.existing_download %}
                                            <p class="mb-0"><small class="text-info">Existing: {{ item.download_analysis.existing_download.status }}</small></p>
                                        {% endif %}
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            {% endfor %}
        </div>
    {% else %}
        <div class="text-center py-5">
            <i class="bi bi-rss text-muted" style="font-size: 4rem;"></i>
            <h3 class="mt-3 text-muted">No RSS Items Found</h3>
            <p class="text-muted">No torrents found in the RSS feed for the last {{ hours }} hours.</p>
        </div>
    {% endif %}
{% endif %}

{% csrf_token %}
{% endblock %}

{% block extra_js %}
<script>
    // RSS trigger functionality (same as dashboard)
    async function triggerRSSCheck() {
        try {
            // Get CSRF token
            const csrfToken = document.querySelector('[name=csrfmiddlewaretoken]').value;

            // Make API call
            const response = await fetch('/api/rss/trigger/', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                    'X-CSRFToken': csrfToken
                },
                body: JSON.stringify({
                    hours: {{ hours }}
                })
            });

            const data = await response.json();

            if (data.success) {
                // Refresh the page to show updated data
                window.location.reload();
            } else {
                alert('RSS check failed: ' + (data.error || 'Unknown error'));
            }

        } catch (error) {
            console.error('RSS check error:', error);
            alert('Failed to trigger RSS check. Please try again.');
        }
    }
</script>
{% endblock %}
