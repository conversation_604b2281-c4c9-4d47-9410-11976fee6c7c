{% extends 'base.html' %}

{% block title %}Settings{% endblock %}

{% block content %}
<div class="container-fluid">
    <div class="row">
        <div class="col-12">
            <div class="d-flex justify-content-between align-items-center mb-4">
                <h1>Settings</h1>
                <a href="{% url 'dashboard' %}" class="btn btn-secondary">
                    <i class="bi bi-arrow-left"></i> Back to Dashboard
                </a>
            </div>

            {% if messages %}
                {% for message in messages %}
                    <div class="alert alert-{{ message.tags }} alert-dismissible fade show" role="alert">
                        {{ message }}
                        <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                    </div>
                {% endfor %}
            {% endif %}

            <form method="post" class="row">
                {% csrf_token %}

                <!-- Download Management Settings -->
                <div class="col-md-6 mb-4">
                    <div class="card">
                        <div class="card-header">
                            <h5 class="card-title mb-0">
                                <i class="bi bi-download"></i> Download Management
                            </h5>
                        </div>
                        <div class="card-body">
                            <div class="mb-3">
                                <div class="form-check">
                                    <input class="form-check-input" type="checkbox"
                                           id="manual_download_always_cleanup"
                                           name="manual_download_always_cleanup"
                                           {% if settings.manual_download_always_cleanup %}checked{% endif %}>
                                    <label class="form-check-label" for="manual_download_always_cleanup">
                                        <strong>Always cleanup manual downloads</strong>
                                    </label>
                                    <div class="form-text">
                                        When enabled, manual downloads will always replace old files regardless of quality.
                                        When disabled, manual downloads only replace files if the new quality is better.
                                    </div>
                                </div>
                            </div>

                            <div class="mb-3">
                                <div class="form-check">
                                    <input class="form-check-input" type="checkbox"
                                           id="enable_plex_cleanup_fallback"
                                           name="enable_plex_cleanup_fallback"
                                           {% if settings.enable_plex_cleanup_fallback %}checked{% endif %}>
                                    <label class="form-check-label" for="enable_plex_cleanup_fallback">
                                        <strong>Enable Plex cleanup fallback</strong>
                                    </label>
                                    <div class="form-text">
                                        When Real Debrid cleanup fails, try to remove old files via Plex API.
                                    </div>
                                </div>
                            </div>

                            <div class="mb-3">
                                <label for="auto_cleanup_quality_threshold" class="form-label">
                                    <strong>Auto cleanup quality threshold</strong>
                                </label>
                                <input type="number" class="form-control"
                                       id="auto_cleanup_quality_threshold"
                                       name="auto_cleanup_quality_threshold"
                                       value="{{ settings.auto_cleanup_quality_threshold }}"
                                       min="0" max="1000" step="10">
                                <div class="form-text">
                                    Quality score difference required for automatic cleanup (0-1000).
                                    Higher values require bigger quality improvements.
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Monitoring Settings -->
                <div class="col-md-6 mb-4">
                    <div class="card">
                        <div class="card-header">
                            <h5 class="card-title mb-0">
                                <i class="bi bi-activity"></i> Download Monitoring
                            </h5>
                        </div>
                        <div class="card-body">
                            <div class="mb-3">
                                <div class="form-check">
                                    <input class="form-check-input" type="checkbox"
                                           id="monitor_downloads_enabled"
                                           name="monitor_downloads_enabled"
                                           {% if settings.monitor_downloads_enabled %}checked{% endif %}>
                                    <label class="form-check-label" for="monitor_downloads_enabled">
                                        <strong>Enable download monitoring</strong>
                                    </label>
                                    <div class="form-text">
                                        Automatically monitor and manage download status.
                                    </div>
                                </div>
                            </div>

                            <div class="mb-3">
                                <label for="monitor_interval_minutes" class="form-label">
                                    <strong>Monitoring interval (minutes)</strong>
                                </label>
                                <input type="number" class="form-control"
                                       id="monitor_interval_minutes"
                                       name="monitor_interval_minutes"
                                       value="{{ settings.monitor_interval_minutes }}"
                                       min="1" max="60">
                                <div class="form-text">
                                    How often to check download status (1-60 minutes).
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Search Settings -->
                <div class="col-md-6 mb-4">
                    <div class="card">
                        <div class="card-header">
                            <h5 class="card-title mb-0">
                                <i class="bi bi-search"></i> Search Settings
                            </h5>
                        </div>
                        <div class="card-body">
                            <div class="mb-3">
                                <label for="jackett_search_timeout" class="form-label">
                                    <strong>Search timeout (seconds)</strong>
                                </label>
                                <input type="number" class="form-control"
                                       id="jackett_search_timeout"
                                       name="jackett_search_timeout"
                                       value="{{ settings.jackett_search_timeout }}"
                                       min="5" max="120">
                                <div class="form-text">
                                    Maximum time to wait for Jackett search results (5-120 seconds).
                                </div>
                            </div>

                            <div class="mb-3">
                                <label for="max_search_results" class="form-label">
                                    <strong>Maximum search results</strong>
                                </label>
                                <input type="number" class="form-control"
                                       id="max_search_results"
                                       name="max_search_results"
                                       value="{{ settings.max_search_results }}"
                                       min="10" max="200" step="10">
                                <div class="form-text">
                                    Maximum number of search results to display (10-200).
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Real Debrid Settings -->
                <div class="col-md-6 mb-4">
                    <div class="card">
                        <div class="card-header">
                            <h5 class="card-title mb-0">
                                <i class="bi bi-cloud-download"></i> Real Debrid Caching
                            </h5>
                        </div>
                        <div class="card-body">
                            <div class="mb-3">
                                <div class="form-check">
                                    <input class="form-check-input" type="checkbox"
                                           id="wait_for_real_debrid_cache"
                                           name="wait_for_real_debrid_cache"
                                           {% if settings.wait_for_real_debrid_cache %}checked{% endif %}>
                                    <label class="form-check-label" for="wait_for_real_debrid_cache">
                                        <strong>Wait for Real Debrid caching</strong>
                                    </label>
                                    <div class="form-text">
                                        When enabled, manual searches will wait for Real Debrid to cache torrents instead of downloading immediately. Automatic downloads are not affected.
                                    </div>
                                </div>
                            </div>

                            <div class="mb-3">
                                <label for="real_debrid_cache_timeout_hours" class="form-label">
                                    <strong>Cache timeout (hours)</strong>
                                </label>
                                <input type="number" class="form-control"
                                       id="real_debrid_cache_timeout_hours"
                                       name="real_debrid_cache_timeout_hours"
                                       value="{{ settings.real_debrid_cache_timeout_hours }}"
                                       min="1" max="168">
                                <div class="form-text">
                                    Maximum hours to wait for caching before giving up (1-168 hours).
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- RSS Monitoring Settings -->
                <div class="card mb-4">
                    <div class="card-header">
                        <h5 class="mb-0">RSS Monitoring</h5>
                    </div>
                    <div class="card-body">
                        <div class="row">
                            <div class="col-md-6">
                                <div class="form-check form-switch mb-3">
                                    <input class="form-check-input" type="checkbox" id="enable_rss_monitoring"
                                           name="enable_rss_monitoring" {% if settings.enable_rss_monitoring %}checked{% endif %}>
                                    <label class="form-check-label" for="enable_rss_monitoring">
                                        Enable RSS Monitoring
                                    </label>
                                    <div class="form-text">
                                        Automatically monitor Jackett RSS feeds every 15 minutes for new torrents and download qualifying ones based on quality profiles.
                                    </div>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label for="rss_monitoring_hours" class="form-label">RSS Lookback Hours</label>
                                    <input type="number" class="form-control" id="rss_monitoring_hours"
                                           name="rss_monitoring_hours" value="{{ settings.rss_monitoring_hours }}"
                                           min="1" max="168">
                                    <div class="form-text">
                                        How many hours to look back in RSS feed for new torrents (1-168 hours).
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Current Settings Info -->
                <div class="col-md-6 mb-4">
                    <div class="card">
                        <div class="card-header">
                            <h5 class="card-title mb-0">
                                <i class="bi bi-info-circle"></i> Information
                            </h5>
                        </div>
                        <div class="card-body">
                            <p class="mb-2">
                                <strong>Last updated:</strong>
                                {{ settings.updated_at|date:"M d, Y H:i" }}
                            </p>
                            <p class="mb-2">
                                <strong>Settings version:</strong>
                                {{ settings.id }}
                            </p>
                            <div class="alert alert-info mb-0">
                                <small>
                                    <i class="bi bi-lightbulb"></i>
                                    <strong>Tip:</strong> Changes take effect immediately.
                                    Some settings may require restarting background processes.
                                </small>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Save Button -->
                <div class="col-12">
                    <div class="d-flex justify-content-end">
                        <button type="submit" class="btn btn-primary btn-lg">
                            <i class="bi bi-check-lg"></i> Save Settings
                        </button>
                    </div>
                </div>
            </form>
        </div>
    </div>
</div>
{% endblock %}
