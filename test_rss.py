#!/usr/bin/env python3
"""
Test script for RSS monitoring functionality
"""

import os
import sys
import django

# Add the project directory to Python path
sys.path.append('.')

# Setup Django
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'overseerr_dashboard.settings')
django.setup()

from media_requests.rss_service import RSSMonitoringService

def test_rss_monitoring():
    """Test RSS monitoring functionality"""
    print("Testing RSS monitoring...")
    
    try:
        rss_service = RSSMonitoringService()
        
        # Test RSS feed fetching (dry run)
        print("Fetching RSS feed...")
        rss_items = rss_service.rss_service.fetch_rss_feed(hours=24)
        
        print(f"Found {len(rss_items)} RSS items")
        
        # Show first few items
        for i, item in enumerate(rss_items[:5]):
            print(f"  {i+1}. {item.title}")
            print(f"     Show: {item.show_title}, S{item.season:02d}E{item.episode:02d}" if item.show_title else "     No episode info")
            print(f"     Quality: {item.quality}, Size: {item.size}")
            print()
        
        return True
        
    except Exception as e:
        print(f"Error testing RSS monitoring: {e}")
        return False

if __name__ == "__main__":
    success = test_rss_monitoring()
    sys.exit(0 if success else 1)
